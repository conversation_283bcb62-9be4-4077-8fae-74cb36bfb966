# GESTION DE CACHE
php artisan config:clear
php artisan route:clear
php artisan view:clear

# VIDER UNE TBALE EN DESACTIVANT LES FOREIGN KEY
SET FOREIGN_KEY_CHECKS=0;
TRUNCATE TABLE document_fichiers;
TRUNCATE TABLE fichiers;
TRUNCATE TABLE memoires;
TRUNCATE TABLE documents;
TRUNCATE TABLE sujet_valeurs;
delete from reference_valeurs where id > 38;
SET FOREIGN_KEY_CHECKS=1;

SET FOREIGN_KEY_CHECKS=0;
delete from reference_valeurs where id > 38;
SET FOREIGN_KEY_CHECKS=1;

composer require doctrine/dbal
composer require wildside/userstamps
<!-- composer require Purifier -->

composer require maatwebsite/excel
# REQUIREMENTS
- PhpSpreadsheet: `^1.21
- PHP extension php_zip enabled
- PHP extension php_xml enabled
- PHP extension php_gd2 enabled
- PHP extension php_iconv enabled
- PHP extension php_simplexml enabled
- PHP extension php_xmlreader enabled
- PHP extension php_zlib enabled


# TODO

## 22/06/2023
- Implementer la fonctionnalite de reponse par mail au cas ou l'utilisateur n'est pas connecte

## 19/06/2023
- [ok] Ajouter une zone de reponse a un message dans la partie client
- [ok] Trouver comment et mettre le bouton de cloture de discussion dans la partie client comme dans la partie admin
- Mettre a jour peridiquement le nombre de messages non traites dans le navbar
- reimplementer la fonctionnalite d'envoi de message par un admin (contacter un client)
- Mettre un numero sur le contact client pour lui signifier qu'il a un nouveau message
- Ajouter une colonne a cet effet dans la table message
- [To-do] Datatable server side processing
- Gestion de role au niveau des professionnels

## 15/06/2023
- Ajouter des icones a la facette de recherche

## 13/06/2023
- [ok] Ramaner la recherche a where
- [ok] Mettre un identifiant sur la grande div des facettes

## 07/06/2023
- [ok] Le filtre doit agir sur les autres options de filtre : Si je filtre par niveau d'étude, les autres options doivent être filtrées en fonction de ce niveau d'étude
- [ok] Ajouter une etiquette en bas de "Filtrer vos recherches" : "Vous avez filtré par : Niveau d'étude : Licence 1, Licence 2, Licence 3"
- [ok] Rentre les sujets cliquables : Lorsque je clique sur un sujet, La page doit doit afficher les documents qui ont ce sujet
- [clotilda] Implementer les actions par la suite : exporter, ...
- [ok] Revoir la fonctionnalité de message : Un message peut avoir une discussion et l'admin ou le client peut cloturer la discussion
- [ok] Ne pas afficher les elements de filtre qui n'ont pas de documents
- [ok] Ajouter l'attribut "closed" a la table message
- [ok] Ajouter la colonne motif dans la liste des messages
- [ok] Modifier le libelle de tri : Titre : A -> Z , Titre : Z -> A
- [...] Dans les facette de recherche, definir un scloll pour les elements qui ont beaucoup de valeurs (5 ou 6 grand maximum)
- [ok] Une zone pour effacer les filtres 

### Liens
- https://mediatheques.vitrolles13.fr/Default/accueil.aspx 
- https://www.saveur-biere.com/fr/4-futs-de-biere/pack_futs-pack_2_futs


## 05/06/2023
- [ok] L'affichage d'un petit message lorsque la recherche est vide n'est pas encore fait

## 01/05/2023
- [ok] Recuperer automatiquement le nombre d'utilisateurs connectes en utilisant ajax

## 29/05/2023
- [ok] Ajouter un loader lors de l'importation de documents
- [ok] Travailler la pagination de sorte a ne pas afficher tous les elements (numero de page)
- [ok] Limiter le nombre d'element des critères de recherche

## 18/05/2023
- [ok] Verifcation de la confirmation de mot de passe lors de la creation d'un compte client

## 14/05/2023
### STEP [002]
- Check if all references are in the database
    - If not in the database, add it
        - Pays de publication
        - Niveau d'étude
        - Institut
        - Langue du document
        - Filière 
        - Site de catalogage
        - Public visé
        <!-- - Diffusé  -->
        - Catégorie 

        - Type de mémoire
        - Sujet 
- Create document after checking if it exists
- Create memoire after checking if it exists


## 11/05/2023
- [ok] Afficher un message lorsque la resultat de recherche est vide
- [ok] [retirer_car_pas_jolie] Sur la page de recherche, mettre un fonds presque jaune sur les zones de filtres
- [ok] [test_a_grande_echelle_a_faire] Importation de document via un fichier excel
- [ok] Afficher les messages non traites sur le dashboard
- [ok] Ajouter les champs de profil dans la partie admin
- [ok] Zone de copyright trop petit et a gauche
- [ok] Version en bas a droite 
- [ok] Faire une verification sur is_public avant d'afficher le detail d'un document
- [ok] Sur l'interface de message, mettre header en blue.
- [ok] Cette couleur doit varier quand c'est favoris, mon compte
- [ok] Fleche d'avant et d'apres dans le detail de document

- [ok] Ajouter Filtrer vos recherches sur la page de recherches

- [ok] [confert001] Retirer la migration de message et refaire car il y a des erreurs (Non creation dela colonne deleted_at)
- [ok] Mettre le bouton de recherche en couleur #EA4F0C


- [ok] Recevoir un fichier 
- [ok] Recevoir le libelle des types de document

### STEP [001]
- Check if migration table contains all migrations names
- Change the three last migration batch to 3
- Do the rollback --step=1 three times (or do the rollback --step=3 once)
- After this, delete the last three migration files
- Update messages migration
- Do the migrate

## 10/05/2023
- [ok] Reproduire les mises en formes du formulaire de recherche
- [ok] [oui] Profil = Mon compte ?

## 09/05/2023
- [ok] Ajouter des icons a l'action d'un utilisateur
- [ok] accès professionnel doit ouvrir un nouvel onglet
- [ok] que la barre de recherche soit bien arrondi  comme sur l’image d’exemple et
- [ok] [revolu] border avec la couleur orange du template comme sur image 3
- [ok] [review] Les éléments en rouges, sont des espaces à supprimer
- [ok] En affichage des résultats, remettre la même barre de recherche que sur la page d’accueil
- [ok] Avant de faire afficher les facettes, avoir une zone de texte où on verra “ affiner votre recherche” qui sera au même niveau que 122 résultats… comme sur la maquette
- [ok] Déplacer la pagination un peu plus vers la droite et
- [ok] [review] le mettre aussi en bas
- [ok] mettre une couleur (#BDD8DC) de fonds comme sur l’image sur l’affichage et les facettes afin de différencier de la couleur de fond générale (couleur blanche ) 
- [ok] Pour aller en affichage détaillé, on doit cliquer sur l’image, faire en sorte que ça soit possible quand on clic aussi sur le titre
- [ok] En affichage détaillé, rajouter une petite flèche retour permettant de revenir à la page des résultats et d’aller sur le document suivant



## 08/05/2023
- [ok] Faire le backend d'envoi de message pour le client
- [ok] Affichage des messages dans la partie client
- [ok] Gestion des messages depuis la partie d'administration


- [ok] Rendre tous les champs de document obligatoire

- [ok] Ajouter un fichier : Le mettre a droit

file:///C:/local/Projects/NUMROD/templates/full-listing-hub-bootstrap-template/_html/vertical-sidebar.html#

file:///C:/local/Projects/NUMROD/templates/full-listing-hub-bootstrap-template/_html/profile-detail.html
Pour l'utilisateur connecté

## 29/04/2023
- [ok] Si le document a un fichier joint, tu peux peux faire en sorte qu'en modification, le premier fichier s'affichage par défaut ?


## 26/04/2023
- [ok] Le voir le crud en rapport avec SUJET ( sujet a été supprime de la table document)
- [ok] Revoir edition de document
- [ok] Remonter le champs de recherche du document (Document-list) en haut

- [ok] Ajout des couleurs au dashboard
    * Documents :  #3390FF
    * Comptes : #33FFA8
    * Références : #FF3383
- [ok] il manque de remettre les textes en minuscules, agrandir considérablement le chiffres
- [ok] Rajouter d’autres zones ( plus tard ou maintenant, à toi de voir )  : 


## 24/04/2023
- Figer la taille du datatable a la hauteur de l'écran et ajouter un scroll en plus de la pagination
- [ok] Document Institut  : Bien gérer l'afficher de reference
- [ok] Document Filière : Bien gérer l'afficher de reference
- [ok] Diminuer la taille de element du Dashboard
- [ok] Ajouter des icons dans le dashboard
- [ok] Ajouter des couleurs
- [ok] Ajouter le copyright
- [ok] Ajouter la version
- [ok] Sidebar et navbar meme couleur
- [ok] Revoir le bouton de déconnexion
- [ok] La zone de fichier associe doit avoir une taille fixe et grande
- [ok] Sujet une selection multiple
- [28/04] Suggestion des auteurs leur de la saisie de ces derniers en utilisant data-list

## 22/04/2023
Dans les champs de création de mémoire 

- [ok] Retirer le champs ( type de mémoire, Catégorie, Thème)
- [OK] Renommer ( public cible par public visé )
- [ok] Que le champ note soit un peu plus grand comme le champ résumé
- [ok] Ajouter un champ ( Diffusé ( Oui ou NON) Oui, le document en question apparaîtra sur la partie publique, NON, cela ne sera pas le cas)

- [ok] Associe donc les références suivantes :  Institut, filière, Site de catalogage


## 20/04/2023
- [ok] En recherche de document ( avoir une colonne  fichier ) Oui si le document a un fichier joint et non si 'est pas le cas
- [ok] Il manque l'action visualiser et supprimer dans Comptes 
- [ok] L'action supprimer dans Document
- Prévoir une photo aussi pour les usagers
- [ok] Afficher le detail de compte


## 19/04/2023
- [ok] Ajouter l'élément "Document" au dashboard
- [ok] Faire une retouche sur les interfaces de gestion de memoire (Enlever la ligne document : le titre )
- [ok] ['Mais pas le backend'] Ajouter l'option de creation de compte usager sur la partie public and son formulaire
- [ok] Ajouter l'option " Voir les details " dans la gestion de memoire : choisir entre le faire sur une nouvelle page ou sur une modal
- [ok] Ajouter l'option d'ajout de photo de couverture d'un memoire 
- Implementation de la corbeille : Avec un filtre pour afficher les elements supprimés 

- [pending] Remettre le code des datatables dans les fichier js

- [ok] Tester la suppression physique des fichiers (suppression des fichiers dans le dossier storage/app/public)
- [ok] Mettre tous les boutons enregistrer en bas et a droite et de couleur verte



# Step [DONE]
- Remove a file from the list
- Add his name in an array
- Add this name to an input hidden which takes many values and will be sent to the controller
- In the controller, we will get the array and we will delete the files in  a loop checking  if the name and index exist in the array
- If he do

# 16/04/2023
- [ok] Qu'est ce qui correspond a l'affichage de des trucs sur mobile (col-md ? xs ? ... ?) [xs]
- How to access to files located in reousrces/css or resources.js ?


# PERSPECTIVE
- [ok] Rediriger l'utilisateur vers la page sur laquelle il voulait aller sans se connecter



Des que tu finis la création des usagers, tu passes à la création des documents ( 1: Mémoire , 2 Livre, 3: Archives 4 : Agenda événement)


# LIENS
## AFFICHAGE DE DOCUMENTS
- https://fr.w3docs.com/tools/code-editor/1807
- https://fliphtml5.com/fr/
- https://fr.w3docs.com/snippets/html/comment-integrer-pdf-dans-html.html

- https://youtu.be/OzlV5Exlp68 



# RAPPORT DU 05/04/2023
Ce qui a été fait :
- Modification de référence
- Suppression de référence ( suppression niveau visuel seulement , pas niveau BD) : Dans quelles conditions supprime t-on une référence ? par qu'elle peut déjà avoir été utilisée au moment où l'on veut faire la suppression
- Enregistrement d'un compte professionnel avec les champs nom, prénom, sexe , type role, identifiant, mot de passe
- Mise en place de la page de modification de compte professionnel

NB:
- Il faut ajouter la référence de type comptes -> sexe avant de faire un enregistrement
- Les options du champ " type de rôle " a été mis par hazard étant donnée que je n'ai pas vu ça dans les references et que je ne sais pas si c'est un choix unique ou multiple

