<?php

namespace App\Http\Controllers;

use App\Imports\MemoireImport;
use App\Models\Document;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class DocumentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $documents = Document::all();
        return view('admin.document-list', compact('documents'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        // Rapport, Presse écrite, Facture, Image/Photo, Actes de colloque, Brevets, Article de presse, 

        $liens = [
            ['Mémoires', 'fa-solid fa-book', 'memoires.create'],
            ['Livres', 'fa-solid fa-book', ''],
            ['Archives', 'fa-solid fa-book', ''],
            ['Rapport', 'fa-solid fa-book', ''],
            ['Presse écrite', 'fa-solid fa-book', ''],
            ['Facture', 'fa-solid fa-book', ''],
            ['Image/Photo', 'fa-solid fa-book', ''],
            ['Actes de colloque', 'fa-solid fa-book', ''],
            ['Brevets', 'fa-solid fa-book', ''],
            ['Article de presse', 'fa-solid fa-book', ''],
        ];
        return view('admin.document-add', compact('liens'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    /**
     * Import document from excel file.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function import(Request $request)
    {
        $request->validate([
            'type_document' => 'required',
            'import' => 'required|mimes:xlsx,xls,csv'
        ]);

        $type = $request->type_document;
        $file = $request->file('import');
        $nbre_ligne = 0;
        switch ($type) {
            case 'Mémoires':
                $import = new MemoireImport($type);
                Excel::import($import, $file);
                if ($import->getStatut() == -1) {
                    return redirect()->back()->with('error', 'Le fichier importé ne contient pas de mémoires');
                }
                $nbre_ligne = $import->getCount();
                break;
            case 'Livres':
                // $import = new LivreImport;
                break;
            case 'Archives':
                // $import = new ArchiveImport;
                break;
        }
        return redirect()->back()->with('success', $nbre_ligne . ' ' . $type . '  importés avec succès');
    }
}
