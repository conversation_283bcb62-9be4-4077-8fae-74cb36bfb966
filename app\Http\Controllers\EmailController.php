<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Mail\ElementsSelectionnesMail;

class EnvoiEmailController extends Controller
{
    public function envoyerEmail(Request $request)
    {
        $elementsSelectionnes = $request->input('elements_selectionnes');

        // Vérifier si des éléments sont sélectionnés
        if (!empty($elementsSelectionnes)) {
            // Envoyer l'e-mail avec les éléments sélectionnés
            Mail::to('<EMAIL>')->send(new ElementsSelectionnesMail($elementsSelectionnes));

            return response()->json(['message' => 'E-mail envoyé avec succès'], 200);
        } else {
            return response()->json(['message' => 'Aucun élément sélectionné'], 400);
        }
    }
    public function sendEmail()
        {
            $data = [
                'name' => '<PERSON> Do<PERSON>',
                'email' => '<EMAIL>',
            ];

            Mail::send('pdf', $data, function ($message) {
                $message->to('<EMAIL>')
                    ->subject('Exemple d\'e-mail en Laravel');
            });

            return 'E-mail envoyé avec succès';
        }

}
