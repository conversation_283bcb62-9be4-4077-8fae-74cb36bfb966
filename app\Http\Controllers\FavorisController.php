<?php

namespace App\Http\Controllers;

use App\Models\Favoris;
use App\Models\Reference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class FavorisController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (!Auth::check()) {
            return redirect()->route('welcome');
        }
        $type = "all";
        $cle = '';
        $sexes = Reference::where('type', 'Comptes')->where('nom', 'Sexe')->first();
        $favoris = Favoris::with('document')
            ->where('user_id', auth()->user()->id)
            ->WhereHas('document', function ($query) {
                $query->where('is_public', 1);
            })
            ->paginate(15);
        return view('client.favoris-list', compact('favoris', 'type', 'cle', 'sexes'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('welcome');
        }
        $is_favoris = false;
        $favoris = Favoris::where('user_id', auth()->user()->id)->where('document_id', $request->id)->first();
        if ($favoris) {
            $favoris->delete();
        } else {
            $favoris = new Favoris();
            $favoris->user_id = auth()->user()->id;
            $favoris->document_id = $request->id;
            $favoris->save();
            $is_favoris = true;
        }
        return response()->json([
            'is_favoris' => $is_favoris,
            'id' => $request->id,
        ]);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Favoris  $favoris
     * @return \Illuminate\Http\Response
     */
    public function show(Favoris $favoris)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Favoris  $favoris
     * @return \Illuminate\Http\Response
     */
    public function edit(Favoris $favoris)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Favoris  $favoris
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, Favoris $favoris)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Favoris  $favoris
     * @return \Illuminate\Http\Response
     */
    public function destroy(Favoris $favoris)
    {
        //
    }
}
