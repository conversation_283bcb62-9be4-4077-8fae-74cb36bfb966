<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\DocumentFichier;
use App\Models\Fichier;
use App\Models\Memoire;
use App\Models\SujetValeur;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MemoireAltController extends Controller
{
    public function disable($document_id) {
        $document_id = explode(',', $document_id);

        // tranformer tous les ements en entiers
        $document_id = array_map(function($item) {
            return (int) $item;
        }, $document_id);

        // Modifier les documents
        Document::whereIn('id', $document_id)->update(['is_public' => false]);
        return $document_id;
    }

    public function delete($document_id) {
        $document_id = explode(',', $document_id);

        // tranformer tous les ements en entiers
        $document_id = array_map(function($item) {
            return (int) $item;
        }, $document_id);

        $fichier_supp_id = DocumentFichier::whereIn('document_id', $document_id)->pluck('fichier_id');

        // Debute la transaction
        DB::beginTransaction();

        try {
            // SUppression de sujet_valeur
            SujetValeur::whereIn('document_id', $document_id)->delete();
            
            // Supprimer le memoire
            Memoire::whereIn('document_id', $document_id)->delete();

            // Supprimer les fichiers dans DocumentFichier
            DocumentFichier::whereIn('document_id', $document_id)->whereIn('fichier_id', $fichier_supp_id)->delete();
            
            // Supprimer le document
            Document::whereIn('id', $document_id)->delete();

            // Supprimer les fichiers
            Fichier::whereIn('id', $fichier_supp_id)->delete();

            DB::commit();

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([], 500);
        }

        return $document_id;
    }
}
