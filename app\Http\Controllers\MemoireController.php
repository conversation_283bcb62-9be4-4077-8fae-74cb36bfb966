<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\DocumentFichier;
use App\Models\Fichier;
use App\Models\Memoire;
use App\Models\Reference;
use App\Models\SujetValeur;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;


class MemoireController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $langues = Reference::where('type', 'Document')->where('nom', 'Langue')->first();
        $instituts = Reference::where('type', 'Document')->where('nom', 'Institut')->first();
        $niveau_etudes = Reference::where('type', 'Document')->where('nom', "Niveau d'étude")->first();
        $sujets = Reference::where('type', 'Document')->where('nom', 'Sujet')->first();
        // $themes = Reference::where('type', 'Document')->where('nom', 'Thème')->first();
        $type_memoires = Reference::where('type', 'Document')->where('nom', 'Type de mémoire')->first();
        $filieres = Reference::where('type', 'Document')->where('nom', 'Filière')->first();
        $categories = Reference::where('type', 'Document')->where('nom', 'Catégorie')->first();
        $pays_publications = Reference::where('type', 'Document')->where('nom', 'Pays de publication')->first();
        $public_cibles = Reference::where('type', 'Document')->where('nom', 'Public visé')->first();
        return view('admin.document-add-memoire', compact(
            'langues',
            'instituts',
            'niveau_etudes',
            'sujets',
            // 'themes',
            'type_memoires',
            'filieres',
            'categories',
            'pays_publications',
            'public_cibles'
        ));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Titre et auteur validation
        $request->validate([
            'titre' => 'required',
            'auteur' => 'required',
        ]);

        $files = $request->file();

        // Decouper les elements en tableau
        $element_supp = explode(',', $request->element_supp);

        // Verifier si la combinaison titre et auteur existe deja
        $document = Document::where('titre', $request->titre)->where('auteur', $request->auteur)->first();
        if ($document) {
            return redirect()->back()->with('error', 'Ce document existe déjà');
        }

        $image = $request->file('image');
        $sujets = $request->sujet;

        // Ajouter le nom de l'image dans element_supp
        if ($image) {
            array_push($element_supp, $image->getClientOriginalName());
        }
        // Debute la transaction
        DB::beginTransaction();

        try {
            // Enregistrer le document
            $document = new Document();
            $document->titre = $request->titre;
            $document->sous_titre = $request->sous_titre;
            $document->auteur = $request->auteur;
            $document->pays_publication = $request->pays_publication;
            $document->resume = $request->resume;
            $document->annee_publication = $request->annee_publication;
            $document->directeur = $request->directeur_memoire;
            $document->langue = $request->langue_document;
            $document->code = $request->code;
            $document->url = $request->url;
            $document->note = $request->note;
            // $document->theme = $request->theme;
            $document->nbr_page = $request->nbr_page;
            $document->autorisation = $request->autorisation;
            $document->materiel_accompagnement = $request->materiel_accompagnement;
            $document->site_catalogage = $request->site_catalogage;
            $document->public_cible = $request->public_cible;
            $document->is_public = intval($request->is_public);
            // $document->categorie = $request->categorie;
            $document->save();

            // Enregistrement de sujet : type array
            foreach ($sujets as $sujet) {
                $sujetvaleur = new SujetValeur();
                $sujetvaleur->document_id = $document->id;
                $sujetvaleur->sujet_id = $sujet;
                $sujetvaleur->save();
            }

            // Enregistrer l'image
            if ($image) {
                $chemin = 'documents/images';
                $extension = $image->getClientOriginalExtension();
                $date = date('dmYhis');
                $fileName = $document->id . '_img_' . $date . '_memoire.' . $extension;
                $image->storeAs('public/' . $chemin, $fileName);
                $cheminFull = 'storage/' . $chemin . '/' . $fileName;

                $fichier = new Fichier();
                $fichier->nom = $fileName;
                $fichier->chemin = $cheminFull;
                $fichier->save();

                $document->image_id = $fichier->id;
                $document->save();
            }

            // Enregistrer les fichiers
            if ($files) {
                $chemin = 'documents/memoires';
                $cpt = 1;
                foreach ($files as $file) {
                    foreach ($file as $item) {
                        $name = $item->getClientOriginalName();
                        // Verifier si le fichier est un element supplementaire
                        if (in_array($name, $element_supp)) {
                            continue;
                        }
                        $extension = $item->getClientOriginalExtension();
                        $date = date('dmYhis');
                        $fileName = $document->id . '_doc_' . $date . '_memoire_' . $cpt . '.' . $extension;
                        $item->storeAs('public/' . $chemin, $fileName);
                        $cheminFull = 'storage/' . $chemin . '/' . $fileName;

                        // Enregistrement du fichier
                        $fichier = new Fichier();
                        $fichier->nom = $fileName;
                        $fichier->chemin = $cheminFull;
                        $fichier->save();

                        // Enregistrement de document_fichier
                        $document_fichier = new DocumentFichier();
                        $document_fichier->document_id = $document->id;
                        $document_fichier->fichier_id = $fichier->id;
                        $document_fichier->save();

                        $cpt++;
                    }
                }
            }


            // Enregistrer le memoire
            $memoire = new Memoire();
            $memoire->document_id = $document->id;
            $memoire->fonction_auteur = $request->fonction_auteur;
            $memoire->niveau_etude = $request->niveau_etude;
            $memoire->institut = $request->institut;
            $memoire->date_soutenance = $request->date_soutenance;
            $memoire->domaine_formation = $request->domaine_formation;
            $memoire->specialite = $request->specialite;
            // $memoire->type_memoire = $request->type_memoire;
            $memoire->filiere = $request->filiere;
            $memoire->mot_cle = $request->mot_cle;
            $memoire->save();


            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'Une erreur est survenue');
        }

        return redirect()->back()->with('success', 'Document ajouté avec succès');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Memoire  $memoire
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $document = Document::find($id);
        if (!$document) {
            return redirect()->back()->with('error', 'Document introuvable');
        }
        return view('admin.document-show-memoire', compact('document'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $document = Document::with('ref_sujet', 'image')->find($id);
        if (!$document) {
            return redirect()->back()->with('error', 'Document introuvable');
        }
        $langues = Reference::where('type', 'Document')->where('nom', 'Langue')->first();
        $instituts = Reference::where('type', 'Document')->where('nom', 'Institut')->first();
        $niveau_etudes = Reference::where('type', 'Document')->where('nom', "Niveau d'étude")->first();
        $sujets = Reference::where('type', 'Document')->where('nom', 'Sujet')->first();
        $themes = Reference::where('type', 'Document')->where('nom', 'Thème')->first();
        $type_memoires = Reference::where('type', 'Document')->where('nom', 'Type de mémoire')->first();
        $filieres = Reference::where('type', 'Document')->where('nom', 'Filière')->first();
        $categories = Reference::where('type', 'Document')->where('nom', 'Catégorie')->first();
        $pays_publications = Reference::where('type', 'Document')->where('nom', 'Pays de publication')->first();
        $public_cibles = Reference::where('type', 'Document')->where('nom', 'Public visé')->first();
        return view('admin.document-edit-memoire', compact(
            'langues',
            'instituts',
            'niveau_etudes',
            'sujets',
            'themes',
            'type_memoires',
            'filieres',
            'categories',
            'document',
            'pays_publications',
            'public_cibles'
        ));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Memoire  $memoire
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // dd($request->file('image'));
        // Titre et auteur validation
        $request->validate([
            'titre' => 'required',
            'auteur' => 'required',
        ]);

        $document = Document::find($id);
        if (!$document) {
            return redirect()->back()->with('error', 'Document introuvable');
        }

        // Verifier si la combinaison titre et auteur a changé
        if ($document->titre != $request->titre || $document->auteur != $request->auteur) {
            // Verifier si la combinaison titre et auteur existe deja
            $document_check = Document::where('titre', $request->titre)->where('auteur', $request->auteur)->first();
            if ($document_check) {
                return redirect()->back()->with('error', 'Ce document existe déjà');
            }
        }

        $image = $request->file('image');

        // Decouper les elements en tableau
        $element_supp = explode(',', $request->element_supp);

        if (($image || !$image) && $document->image_id) {
            // Ajouter le nom de l'image dans element_supp
            array_push($element_supp, $document->image->nom);
        }

        // recuperer les id des fichiers a supprimer
        $fichiers_supp_id = Fichier::whereIn('nom', $element_supp)->pluck('id');

        // Supprimer les fichiers dans DocumentFichier
        DocumentFichier::where('document_id', $document->id)->whereIn('fichier_id', $fichiers_supp_id)->delete();

        // Supprimer les fichiers dans Fichier
        Fichier::whereIn('id', $fichiers_supp_id)->delete();

        $chemin = 'documents/memoires';

        $image = $request->file('image');

        // Ajouter le nom de l'image dans element_supp
        if ($image) {
            array_push($element_supp, $image->getClientOriginalName());
        }

        // Recuperer les fichiers
        $files = $request->file();

        // Traiter des fichiers
        $sujetChange = false;
        $original = $document->ref_sujet->pluck('sujet_id')->toArray();
        $nouveau = $request->sujets;
        if (count($original) == 0) {
            $original = [];
        }
        if (!$nouveau) {
            $nouveau = [];
        }
        $nouvelles_valeurs = array_diff($nouveau, $original);
        $valeurs_disparues = array_diff($original, $nouveau);
        if (count($nouvelles_valeurs) > 0 || count($valeurs_disparues) > 0) {
            $sujetChange = true;
        }

        // Debuter la transaction
        DB::beginTransaction();

        try {
            // Enregistrer le document
            $document = Document::find($id);
            $document->titre = $request->titre;
            $document->sous_titre = $request->sous_titre;
            $document->auteur = $request->auteur;
            $document->pays_publication = $request->pays_publication;
            $document->resume = $request->resume;
            $document->annee_publication = $request->annee_publication;
            $document->directeur = $request->directeur_memoire;
            $document->langue = $request->langue_document;
            $document->code = $request->code;
            $document->url = $request->url;
            $document->note = $request->note;
            // $document->theme = $request->theme;
            $document->nbr_page = $request->nbr_page;
            $document->autorisation = $request->autorisation;
            $document->materiel_accompagnement = $request->materiel_accompagnement;
            $document->site_catalogage = $request->site_catalogage;
            $document->public_cible = $request->public_cible;
            $document->is_public = intval($request->is_public);
            // $document->categorie = $request->categorie;

            // Enregistrer les sujets
            if ($sujetChange) {
                // Supprimer les sujets disparus
                SujetValeur::where('document_id', $document->id)->whereIn('sujet_id', $valeurs_disparues)->delete();

                // Enregistrer les nouveaux sujets
                foreach ($nouvelles_valeurs as $sujet_id) {
                    $sujet = new SujetValeur();
                    $sujet->document_id = $document->id;
                    $sujet->sujet_id = $sujet_id;
                    $sujet->save();
                }
            }

            if (($image || !$image) && $document->image_id) {
                $document->image_id = null;
            }

            if ($image) {
                // Enregistrement de l'image en local
                $name = $image->getClientOriginalName();
                $extension = $image->getClientOriginalExtension();
                $date = date('dmYhis');
                $fileName = $document->id . '_img_' . $date . '_memoire.' . $extension;
                $image->storeAs('public/' . $chemin, $fileName);
                $cheminFull = 'storage/' . $chemin . '/' . $fileName;

                // Enregistrement de l'image dans la BD
                $fichier = new Fichier();
                $fichier->nom = $fileName;
                $fichier->chemin = $cheminFull;
                $fichier->save();

                $document->image_id = $fichier->id;
            }

            $document->save();

            // Enregistrer les fichiers
            if ($files) {
                $cpt = 1;
                foreach ($files as $file) {
                    foreach ($file as $item) {
                        $name = $item->getClientOriginalName();
                        // Verifier si le fichier est un element supplementaire
                        if (in_array($name, $element_supp)) {
                            continue;
                        }
                        $extension = $item->getClientOriginalExtension();
                        $date = date('dmYhis');
                        $fileName = $document->id . '_doc_' . $date . '_memoire_' . $cpt . '.' . $extension;
                        $item->storeAs('public/' . $chemin, $fileName);
                        $cheminFull = 'storage/' . $chemin . '/' . $fileName;

                        // Enregistrement du fichier
                        $fichier = new Fichier();
                        $fichier->nom = $fileName;
                        $fichier->chemin = $cheminFull;
                        $fichier->save();

                        // Enregistrement de document_fichier
                        $document_fichier = new DocumentFichier();
                        $document_fichier->document_id = $document->id;
                        $document_fichier->fichier_id = $fichier->id;
                        $document_fichier->save();

                        $cpt++;
                    }
                }
            }

            // Enregistrer le memoire
            $memoire = Memoire::where('document_id', $id)->first();
            $memoire->fonction_auteur = $request->fonction_auteur;
            $memoire->niveau_etude = $request->niveau_etude;
            $memoire->institut = $request->institut;
            $memoire->date_soutenance = $request->date_soutenance;
            $memoire->domaine_formation = $request->domaine_formation;
            $memoire->specialite = $request->specialite;
            // $memoire->type_memoire = $request->type_memoire;
            $memoire->filiere = $request->filiere;
            $memoire->mot_cle = $request->mot_cle;
            $memoire->save();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'Une erreur est survenue');
        }

        return redirect()->route('documents.index')->with('success', 'Document modifié avec succès');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Memoire  $memoire
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = [];

        // Recuperer le document selon l'id s'il existe
        $document = Document::find($id);
        if (!$document) {
            // Assign error message
            $data['success'] = false;
            $data['message'] = 'Document introuvable';
            return response()->json($data);
        }

        $fichier_supp_id = DocumentFichier::where('document_id', $document->id)->pluck('fichier_id');

        // Debute la transaction
        DB::beginTransaction();

        try {
            // SUppression de sujet_valeur
            SujetValeur::where('document_id', $document->id)->delete();

            // Supprimer le memoire
            Memoire::where('document_id', $document->id)->delete();

            // Supprimer les fichiers dans DocumentFichier
            DocumentFichier::where('document_id', $document->id)->whereIn('fichier_id', $fichier_supp_id)->delete();

            // Supprimer le document
            $document->delete();

            // Supprimer les fichiers
            Fichier::whereIn('id', $fichier_supp_id)->delete();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            // Assign error message
            $data['success'] = false;
            $data['message'] = 'Une erreur est survenue';
            return response()->json($data);
        }

        // Assign success message
        $data['success'] = true;
        $data['message'] = 'Document supprimé avec succès';
        // Id supp
        $data['id'] = $fichier_supp_id;
        return response()->json($data);
    }
}
