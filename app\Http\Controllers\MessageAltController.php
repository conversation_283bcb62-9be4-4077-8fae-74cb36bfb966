<?php

namespace App\Http\Controllers;

use App\Models\Discussion;
use App\Models\Message;
use App\Models\Usager;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MessageAltController extends Controller
{
    // Gestion des messages par l'administrateur

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        // $messages = Message::where('sent_by_admin', false)->paginate(20);
        $messages = Message::all(); //paginate(20);
        return view('admin.message-list', compact('messages'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $usagers = Usager::with('compte')->get();
        $clients = Message::select('id', 'nom', 'prenom', 'email')
            ->where('nom', '!=', null)
            ->distinct()
            ->get();
        return view('admin.message-show-alt', compact('usagers', 'clients'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Message  $message
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $message = Message::with('user', 'discussions')->find($id);
        if (!$message) {
            return back()->with('error', 'Ce message n\'existe pas');
        }
        if ($message->lu == false) {
            $message->lu = true;
            $message->save();
        }
        return view('admin.message-show', compact('message'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Message  $message
     * @return \Illuminate\Http\Response
     */
    public function edit(Message $message)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Message  $message
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        DB::beginTransaction();

        try {
            $message = Message::find($id);
            if (!$message) {
                return response()->json(['message' => 'Ce message n\'existe pas'], 500);
            }
            // $message->reponse = $request->message;
            $message->repondu = true;
            $message->save();

            $discussion = new Discussion();
            $discussion->message_id = $message->id;
            $discussion->message = $request->message;
            $discussion->sent_by_admin = true;
            $discussion->save();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => 'Une erreur est survenue lors de l\'enregistrement de la réponse'], 500);
        }

        $data = [
            'id' => $message->id,
            'reponse' => $discussion->message,
            'date' => $message->created_at->format('d-m-Y H:i:s'),
        ];

        return $data;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Message  $message
     * @return \Illuminate\Http\Response
     */
    public function destroy(Message $message)
    {
        //
    }

    /**
     * Send message to client
     *
     * @param  \App\Models\Message  $message
     * @return \Illuminate\Http\Response
     */
    public function contact(Request $request)
    {
        $id = $request->dest;
        DB::beginTransaction();

        try {
            $message = new Message();
            if ($request->type == 'usr') {
                $usager = Usager::with('compte')->find($id);
                if (!$usager) {
                    return response()->json(['message' => 'Cet usager n\'existe pas'], 500);
                }
                $message->user_id = $usager->compte->user_id;
            } else {
                $user = Message::find($id);
                if (!$user) {
                    return response()->json(['message' => 'Cet utilisateur n\'existe pas'], 500);
                }
                $message->nom = $user->nom;
                $message->prenom = $user->prenom;
                $message->email = $user->email;
            }
            $message->autre_motif = '-';
            $message->lu = true;
            $message->repondu = true;
            $message->save();

            $discussion = new Discussion();
            $discussion->message_id = $message->id;
            $discussion->message = $request->message;
            $discussion->sent_by_admin = true;
            $discussion->save();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['message' => 'Une erreur est survenue lors de l\'enregistrement de la réponse'], 500);
        }

        // Envoie de mail

        return response()->json(
            [
                'message' => 'Message envoyé avec succès',
                'reponse' => $discussion->message,
                'date' => $discussion->created_at->format('d-m-Y H:i:s'),
            ],
            200,
        );
    }

    /**
     * Close message
     *
     * @param  \App\Models\Message  $message
     * @return \Illuminate\Http\Response
     */
    public function closeDiscussion($id)
    {
        $message = Message::find($id);
        if (!$message) {
            return response()->json(['message' => 'Ce message n\'existe pas'], 500);
        }
        $message->closed = true;
        $message->save();
        return response()->json(['message' => 'Message fermé avec succès'], 200);
    }

    /**
     * CLose message permanently
     *
     * @param  \App\Models\Message  $message
     * @return \Illuminate\Http\Response
     */
    public function closeDiscussionPermanently($id)
    {
        $message = Message::find($id);
        if (!$message) {
            return response()->json(['message' => 'Ce message n\'existe pas'], 500);
        }
        $message->closed = true;
        $message->closed_permanently = true;
        $message->save();
        return response()->json(['message' => 'Message fermé avec succès'], 200);
    }
}
