<?php

namespace App\Http\Controllers;

use App\Models\Discussion;
use App\Models\Message;
use App\Models\Reference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class MessageController extends Controller
{
    // Gestion des messages par le client

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $type = 'all';
        $cle = '';
        if (!auth()->check()) {
            return redirect()->route('messages.create');
        }
        $sexes = Reference::with('reference_valeurs')
            ->where('type', 'Comptes')
            ->where('nom', 'Sexe')
            ->first();
        $messages = Message::with('discussions')
            ->where('user_id', auth()->user()->id)
            ->orderBy('created_at', 'desc')
            ->where('autre_motif', '!=', '-')
            ->paginate(20);
        return view('client.message-list', compact('sexes', 'messages', 'type', 'cle'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $type = 'all';
        $cle = '';
        $motifs = Reference::where('type', 'Contact')
            ->where('nom', 'Motif')
            ->first();
        $sexes = Reference::where('type', 'Comptes')
            ->where('nom', 'Sexe')
            ->first();
        return view('client.message-add', compact('sexes', 'motifs', 'type', 'cle'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (!auth()->check()) {
            $request->validate([
                'nom' => 'required',
                'prenom' => 'required',
                'email' => 'required|email',
                'telephone' => 'required',
                'motif' => 'required',
                'message' => 'required',
            ]);
        } else {
            $request->validate([
                'motif' => 'required',
                'message' => 'required',
            ]);
        }

        DB::beginTransaction();

        try {
            $message = new Message();
            $message->nom = $request->nom;
            $message->prenom = $request->prenom;
            $message->email = $request->email;
            $message->telephone = $request->telephone;
            if ($request->motif == '0') {
                $message->autre_motif = $request->autre_motif;
            } else {
                $message->motif = $request->motif;
            }
            $message->lu = false;
            $message->repondu = false;
            if (auth()->check()) {
                $message->user_id = auth()->user()->id;
            }
            $message->save();

            $discussion = new Discussion();
            $discussion->message_id = $message->id;
            $discussion->sent_by_admin = false;
            $discussion->message = $request->message;
            $discussion->save();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Une erreur est survenue lors de l\'envoi de votre message');
        }

        return back()->with('success', 'Votre message a été envoyé avec succès');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Message  $message
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        if (!auth()->check()) {
            return redirect()->route('messages.create');
        }
        $type = 'all';
        $cle = '';
        $sexes = Reference::where('type', 'Comptes')
            ->where('nom', 'Sexe')
            ->first();
        $message = Message::with('discussions')->find($id);
        if (!$message) {
            return back()->with('error', 'Ce message n\'existe pas');
        }
        return view('client.message-show', compact('message', 'type', 'cle', 'sexes'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Message  $message
     * @return \Illuminate\Http\Response
     */
    public function edit(Message $message)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Message  $message
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        if (!auth()->check()) {
            return redirect()->route('messages.create');
        }

        $message = Message::find($id);
        if (!$message) {
            return response()->json(['message' => 'Ce message n\'existe pas'], 500);
        }

        if ($message->closed) {
            return response()->json(['message' => 'Ce discussion est cloturée'], 500);
        }

        $discussion = new Discussion();
        $discussion->message_id = $message->id;
        $discussion->message = $request->message;
        $discussion->sent_by_admin = false;
        $discussion->save();

        $data = [
            'id' => $message->id,
            'reponse' => $discussion->message,
            'date' => $message->created_at->format('d-m-Y H:i:s'),
        ];

        return $data;
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Message  $message
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    /**
     * Close a discussion
     *
     * @param  $id
     * @return \Illuminate\Http\Response
     */
    public function closeDiscussion($id)
    {
        $data = [];
        if (!auth()->check()) {
            $data['message'] = "Veuillez vous connecter pour poursuivre";
            return response()->json($data, 400);
        }
        // Close a discussion
        $message = Message::find($id);
        if (!$message) {
            $data['message'] = 'Ce message n\'existe pas';
            return response()->json($data, 400);
        }
        $message->closed = true;
        $message->save();

        $data['message'] = 'Discussion cloturée avec succès';

        return response()->json($data, 200);
    }
}
