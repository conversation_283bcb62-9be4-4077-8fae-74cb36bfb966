<?php

namespace App\Http\Controllers;

use App\Models\Compte;
use App\Models\Professionnel;
use App\Models\Reference;
use App\Models\ReferenceValeur;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProfessionnelController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $sexes = Reference::where('type', 'Comptes')->where('nom', 'Sexe')->first();
        $classes = Reference::where('type', 'Comptes')->where('nom', 'Classe')->first();
        $groupes = Reference::where('type', 'Comptes')->where('nom', 'Groupe')->first();
        $services = Reference::where('type', 'Comptes')->where('nom', 'Service')->first();
        $villes = Reference::where('type', 'Comptes')->where('nom', 'Ville')->first();
        $sites = Reference::where('type', 'Exemplaire')->where('nom', 'Site/Etablissement')->first();

        return view('admin.compte-add-pro', compact('sexes', 'classes', 'groupes', 'services', 'villes', 'sites'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Verifier la saisie du nom, prenom, sexe, type_role, username, password
        $request->validate([
            'nom' => 'required',
            'prenom' => 'required',
            'sexe' => 'required',
            'username' => 'required',
            'mot_de_passe' => 'required',
        ]);

        // Verifier si le username existe deja
        $user = User::where('username', $request->username)->first();
        if ($user) {
            return back()->with('error', 'Ce nom d\'utilisateur existe deja');
        }

        // Verifier si l'email existe deja
        if ($request->email != '') {
            $user = User::where('email', $request->email)->first();
            if ($user) {
                return back()->with('error', 'Cet email existe deja');
            }
        }


        // Verifier si la combinaison nom et prenom existe deja
        $user = User::where('nom', $request->nom)->where('prenom', $request->prenom)->first();
        if ($user) {
            return back()->with('error', 'Ce nom et prenom existe deja');
        }

        DB::beginTransaction();

        try {
            // Enregistrer les donnees dans la table user
            $user = new User();
            $user->nom = $request->nom;
            $user->prenom = $request->prenom;
            $user->email = $request->email;
            $user->is_active = intval($request->actif);
            $user->username = $request->username;
            $user->password = bcrypt($request->mot_de_passe);
            $user->is_admin = 1;
            $user->save();

            // Enregistrer les donnees dans la table compte
            $compte = new Compte();
            $compte->date_naissance = $request->date_naissance;
            $compte->groupe = $request->groupe;
            $compte->telephone = $request->telephone;
            $compte->adresse = $request->adresse;
            $compte->ville = $request->ville;
            $compte->commentaire = $request->commentaire;
            $compte->sexe = $request->sexe;
            $compte->user_id = $user->id;
            $compte->save();

            // Enregistrer les donnees dans la table professionnel
            $professionnel = new Professionnel();
            // $usager->type_role = intval($request->type_role);
            $professionnel->rattache = $request->rattacher;
            $professionnel->service = $request->service;
            $professionnel->compte_id = $compte->id;
            $professionnel->save();

            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Erreur lors de l\'enregistrement');
        }

        return back()->with('success', 'Compte professionnel enregistre avec succes');
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\Professionnel  $professionnel
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $compte = Compte::with('professionnel', 'user')->find($id);

        // Verifier si le compte existe
        if (!$compte) {
            return back()->with('error', 'Ce compte n\'existe pas');
        }

        return view('admin.compte-show-pro', compact('compte'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  $id
     * @return \Illuminate\Http\Response
     */

    public function edit($id)
    {
        $sexes = Reference::where('type', 'Comptes')->where('nom', 'Sexe')->first();
        $classes = Reference::where('type', 'Comptes')->where('nom', 'Classe')->first();
        $groupes = Reference::where('type', 'Comptes')->where('nom', 'Groupe')->first();
        $services = Reference::where('type', 'Comptes')->where('nom', 'Service')->first();
        $villes = Reference::where('type', 'Comptes')->where('nom', 'Ville')->first();
        $sites = Reference::where('type', 'Exemplaire')->where('nom', 'Site/Etablissement')->first();
        $compte = Compte::find($id);

        return view('admin.compte-edit-pro', compact('compte', 'sexes', 'classes', 'groupes', 'services', 'villes', 'sites'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\Professionnel  $professionnel
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // Verifier la saisie du nom, prenom, sexe, username
        $request->validate([
            'nom' => 'required',
            'prenom' => 'required',
            'sexe' => 'required',
            'username' => 'required',
        ]);

        // Verifier si le username a ete modifié
        $compte = Compte::find($id);
        $user = User::find($compte->user_id);

        if ($user->username != $request->username) {
            // Verifier si le username existe deja
            $user = User::where('username', $request->username)->first();
            if ($user) {
                return back()->with('error', 'Ce nom d\'utilisateur existe deja');
            }
        }

        // Verifier si l'email changé
        if ($user->email != $request->email) {
            // Verifier si l'email existe deja
            if ($request->email != '' && $user->email != $request->email) {
                $user = User::where('email', $request->email)->first();
                if ($user) {
                    return back()->with('error', 'Cet email existe deja');
                }
            }
        }


        // Verifier si la combinaison nom et prenom existe deja
        // $user = User::where('nom', $request->nom)->where('prenom', $request->prenom)->first();


        DB::beginTransaction();

        try {
            // Enregistrer les donnees dans la table user
            $compte = Compte::find($id);
            $user = User::find($compte->user_id);
            $professionnel = Professionnel::where('compte_id', $compte->id)->first();

            $user->nom = $request->nom;
            $user->prenom = $request->prenom;
            $user->email = $request->email;
            $user->is_active = intval($request->actif);
            $user->username = $request->username;
            $user->password = bcrypt($request->password);
            $user->save();

            // Enregistrer les donnees dans la table compte
            $compte->date_naissance = $request->date_naissance;
            $compte->groupe = $request->groupe;
            $compte->telephone = $request->telephone;
            $compte->adresse = $request->adresse;
            $compte->ville = $request->ville;
            $compte->commentaire = $request->commentaire;
            $compte->sexe = $request->sexe;
            $compte->user_id = $user->id;
            $compte->save();

            // Enregistrer les donnees dans la table professionnel
            // $professionnel = new Professionnel();

            // $usager->type_role = intval($request->type_role);
            $professionnel->rattache = $request->rattacher;
            $professionnel->service = $request->service;
            $professionnel->save();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Erreur lors de la modification');
        }

        return redirect()->route('comptes.index')->with('success', 'Compte professionnel modifié avec succes');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\Professionnel  $professionnel
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = [];

        // Recuperer le compte selon l'id s'il existe
        $compte = Compte::find($id);
        $pro = Professionnel::where('compte_id', $id)->first();
        if (!$compte || !$pro) {
            // Assign error message
            $data['success'] = false;
            $data['message'] = 'Compte introuvable';
            return response()->json($data);
        }

        // Supprimer le compte
        $compte->delete();
        $pro->delete();

        // Assign success message
        $data['success'] = true;
        $data['message'] = 'Compte supprimé avec succès';
        return response()->json($data);
    }
}
