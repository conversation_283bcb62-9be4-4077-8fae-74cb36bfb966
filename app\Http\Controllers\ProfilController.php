<?php

namespace App\Http\Controllers;

use App\Models\Compte;
use App\Models\Reference;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ProfilController extends Controller
{
    // Pour les clients
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        if (!Auth::check()) {
            return back()->with('error', 'Vous devez vous connecter pour effectuer cette action');
        }
        $user = User::with('compte')->find(Auth::user()->id);
        $type = '';
        $cle = '';
        $sexes = Reference::where('type', 'Comptes')->where('nom', 'Sexe')->first();

        return view('client.profil', compact('sexes', 'type', 'cle', 'user'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (!Auth::check()) {
            return back()->with('error', 'Vous devez vous connecter pour effectuer cette action');
        }

        $request->validate([
            'nom' => 'required',
            'prenom' => 'required',
            'email' => 'required',
            'password1' => 'required',
        ]);

        // Verifier si le user est authentique
        if (!Auth::attempt(['username' => Auth::user()->username, 'password' => $request->password1])) {
            return back()->with('error', 'Mot de passe incorrect');
        }

        // Verifier l'unicité des données : nom-prenom, email, username
        $user = User::where('nom', $request->nom)->where('prenom', $request->prenom)->count();
        if ($user > 1) {
            return back()->with('error', 'Cet utiisateur n\'existe deja');
        }

        $user = User::where('email', $request->email)->count();
        if ($user > 1) {
            return back()->with('error', 'Cet email existe deja');
        }

        $user = User::where('username', $request->username)->count();
        if ($user > 1) {
            return back()->with('error', 'Ce nom d\'utilisateur existe deja');
        }
        $user = User::find(Auth::user()->id);
        if (!$user) {
            return back()->with('error', 'Cet utiisateur n\'existe pas');
        }
        
        DB::beginTransaction();
        try {
            $user->nom = $request->nom;
            $user->prenom = $request->prenom;
            $user->email = $request->email;
            $user->username = $request->username;
            $user->password = $request->password ? bcrypt($request->password) : $user->password;
            $user->save();
            
            if (!Auth::user()->is_admin) {
                $compte = Compte::where('user_id', Auth::user()->id)->first();
                $compte->telephone = $request->telephone;
                $compte->save();
            }

            DB::commit();
            
            return back()->with('success', 'Profil modifié avec succès');
        } catch (\Throwable $th) {
            DB::rollback();
            return back()->with('error', 'Erreur lors de la modification du profil');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
