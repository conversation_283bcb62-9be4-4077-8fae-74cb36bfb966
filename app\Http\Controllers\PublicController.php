<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\Favoris;
use App\Models\Reference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Auth;

use Illuminate\Support\Facades\Mail;
use App\Mail\ElementsSelectionnesMail;

class PublicController extends Controller
{
    //
    public function manage()
    {
        return view('client.manager_listing');
    }

    public function rechercheAnnonce(Request $request)
    {
        $nbreResultat = 0;
        $nbreMemoire = 0;
        $facettes = [];
        $document_ids = [];
        $siteCatalogues = [];
        $filiers = [];
        $typeMemoires = [];
        $sujets = [];
        $publicCibles  = [];
        $domaineFormations = [];
        $type = $request->type_document;
        $cle = $request->mot_cle;
        $niveauEtudes = [];
        $auteurs = [];
        $directeurs = [];
        $filtre = json_decode($request->input('filtre', ''), true);
        $filtres = json_decode($request->input('filtres', ''), true);

        $filtre = !$filtre ? [] : $filtre;
        $filtres = !$filtres ? [] : $filtres;

        if ($filtre != [] && $filtre['isChecked']) {
            $filtres[] = $filtre;
        }

        if ($filtre != [] && !$filtre['isChecked']) {
            // Mettre le isChecked à 1 pour avoir une correspondance avec le tableau des filtres
            $filtre['isChecked'] = 1;

            foreach ($filtres as $key => $item) {
                if (count(array_diff($item, $filtre)) === 0) {
                    unset($filtres[$key]);
                    break;
                }
            }
        }

        $sexes = Reference::where('type', 'Comptes')->where('nom', 'Sexe')->first();
        $documents = $this->baseFilter($type, $cle);
        switch ($type) {
            case 'all':

                $nbreMemoire = $documents->count();
                $nbreResultat += $nbreMemoire;
                //dd($documents->get());
                if (!empty($filtres)) {
                    foreach ($filtres as $key => $filtre) {
                        $col_tmp = $filtre['type'];
                        switch ($col_tmp) {
                            case 'document':
                                $documents = $documents->where($filtre['colonne'], $filtre['id']);
                                break;
                            case 'memoire':
                                $documents = $documents->whereHas($filtre['type'], function ($query) use ($filtre) {
                                    $query->where($filtre['colonne'], $filtre['id']);
                                });
                                break;
                            case 'ref_sujet':
                                $documents = $documents->whereHas($filtre['type'], function ($query) use ($filtre) {
                                    $query->where('sujet_id', $filtre['id']);
                                });
                                break;
                            default:
                                break;
                        }
                    }
                }

                foreach ($documents->get() as $document) {
                    if ($document->site_catalogage) {
                        $id = $document->site_catalogage;
                        $valeur = $document->ref_site_catalogage->valeur;
                        $nbre = $document->where('site_catalogage', $id)->count();
                        $isChecked = false;

                        foreach ($filtres as $key => $value) {
                            if ($id == $value['id']) {
                                $isChecked = true;
                                break;
                            }
                        }

                        $element_tmp = [
                            'id' => $id,
                            'valeur' => $valeur,
                            'nbre' => $nbre,
                            'isChecked' => $isChecked,
                            'slug' => PublicController::slugify($id),
                        ];

                        array_push($siteCatalogues, $element_tmp);
                    }

                    if ($document->public_cible) {
                        $id = $document->public_cible;
                        $valeur = $document->ref_public_cible->valeur;
                        $nbre = $document->where('public_cible', $id)->count();
                        $isChecked = false;

                        foreach ($filtres as $key => $value) {
                            if ($id == $value['id']) {
                                $isChecked = true;
                                break;
                            }
                        }

                        $element_tmp = [
                            'id' => $id,
                            'valeur' => $valeur,
                            'nbre' => $nbre,
                            'isChecked' => $isChecked,
                            'slug' => PublicController::slugify($id),
                        ];

                        array_push($publicCibles, $element_tmp);
                    }

                    foreach ($document->ref_sujet as $sujet) {
                        $id = $sujet->sujet_id;
                        $valeur = $sujet->sujet->valeur;
                        $nbre = $document->WhereHas('ref_sujet', function ($query) use ($id) {
                            $query->where('sujet_id', $id);
                        })->count();
                        $isChecked = false;

                        foreach ($filtres as $key => $value) {
                            if ($id == $value['id']) {
                                $isChecked = true;
                                break;
                            }
                        }

                        $element_tmp = [
                            'id' => $id,
                            'valeur' => $valeur,
                            'nbre' => $nbre,
                            'isChecked' => $isChecked,
                            'slug' => PublicController::slugify($id),
                        ];

                        array_push($sujets, $element_tmp);
                    }

                    if ($document->memoire->filiere) {
                        $id = $document->memoire->filiere;
                        $valeur = $document->memoire->ref_filiere->valeur;
                        $nbre = $document->WhereHas('memoire', function ($query) use ($id) {
                            $query->where('filiere', $id);
                        })->count();
                        $isChecked = false;

                        foreach ($filtres as $key => $value) {
                            if ($id == $value['id']) {
                                $isChecked = true;
                                break;
                            }
                        }

                        $element_tmp = [
                            'id' => $id,
                            'valeur' => $valeur,
                            'nbre' => $nbre,
                            'isChecked' => $isChecked,
                            'slug' => PublicController::slugify($id),
                        ];

                        array_push($filiers, $element_tmp);
                    }

                    if ($document->memoire->type_memoire) {
                        $id = $document->memoire->type_memoire;
                        $valeur = $document->memoire->ref_type_memoire->valeur;
                        $nbre = $document->WhereHas('memoire', function ($query) use ($id) {
                            $query->where('type_memoire', $id);
                        })->count();
                        $isChecked = false;

                        foreach ($filtres as $key => $value) {
                            if ($id == $value['id']) {
                                $isChecked = true;
                                break;
                            }
                        }

                        $element_tmp = [
                            'id' => $id,
                            'valeur' => $valeur,
                            'nbre' => $nbre,
                            'isChecked' => $isChecked,
                            'slug' => PublicController::slugify($id),
                        ];

                        array_push($typeMemoires, $element_tmp);
                    }

                    if ($document->memoire->niveau_etude) {
                        $id = $document->memoire->niveau_etude;
                        $valeur = $document->memoire->ref_niveau_etude->valeur;
                        $nbre = $document->WhereHas('memoire', function ($query) use ($id) {
                            $query->where('niveau_etude', $id);
                        })->count();
                        $isChecked = false;

                        foreach ($filtres as $key => $value) {
                            if ($id == $value['id']) {
                                $isChecked = true;
                                break;
                            }
                        }

                        $element_tmp = [
                            'id' => $id,
                            'valeur' => $valeur,
                            'nbre' => $nbre,
                            'isChecked' => $isChecked,
                            'slug' => PublicController::slugify($id),
                        ];

                        array_push($niveauEtudes, $element_tmp);
                    }

                    if ($document->auteur) {
                        $id = $document->auteur;
                        $valeur = $document->auteur;
                        $nbre = $document->where('auteur', $id)->count();
                        $isChecked = false;

                        foreach ($filtres as $key => $value) {
                            if ($id == $value['id']) {
                                $isChecked = true;
                                break;
                            }
                        }

                        $element_tmp = [
                            'id' => $id,
                            'valeur' => $valeur,
                            'nbre' => $nbre,
                            'isChecked' => $isChecked,
                            'slug' => PublicController::slugify($id),
                        ];

                        array_push($auteurs, $element_tmp);
                    }

                    if ($document->memoire->domaine_formation) {
                        $id = $document->memoire->domaine_formation;
                        $valeur = $document->memoire->domaine_formation;
                        $nbre = $document->memoire->where('domaine_formation', $id)->count();
                        $isChecked = false;

                        foreach ($filtres as $key => $value) {
                            if ($id == $value['id']) {
                                $isChecked = true;
                                break;
                            }
                        }

                        $element_tmp = [
                            'id' => $id,
                            'valeur' => $valeur,
                            'nbre' => $nbre,
                            'isChecked' => $isChecked,
                            'slug' => PublicController::slugify($id),
                        ];

                        array_push($domaineFormations, $element_tmp);
                    }

                    if ($document->directeur) {
                        $id = $document->directeur;
                        $valeur = $document->directeur;
                        $nbre = $document->where('directeur', $id)->count();
                        $isChecked = false;

                        foreach ($filtres as $key => $value) {
                            if ($id == $value['id']) {
                                $isChecked = true;
                                break;
                            }
                        }

                        $element_tmp = [
                            'id' => $id,
                            'valeur' => $valeur,
                            'nbre' => $nbre,
                            'isChecked' => $isChecked,
                            'slug' => PublicController::slugify($id),
                        ];

                        array_push($directeurs, $element_tmp);
                    }
                }

                $siteCatalogues = array_map("unserialize", array_unique(array_map("serialize", $siteCatalogues)));
                $filiers = array_map("unserialize", array_unique(array_map("serialize", $filiers)));
                $typeMemoires = array_map("unserialize", array_unique(array_map("serialize", $typeMemoires)));
                $sujets = array_map("unserialize", array_unique(array_map("serialize", $sujets)));
                $publicCibles = array_map("unserialize", array_unique(array_map("serialize", $publicCibles)));
                $niveauEtudes = array_map("unserialize", array_unique(array_map("serialize", $niveauEtudes)));
                $domaineFormations = array_map("unserialize", array_unique(array_map("serialize", $domaineFormations)));
                $auteurs = array_map("unserialize", array_unique(array_map("serialize", $auteurs)));
                $directeurs = array_map("unserialize", array_unique(array_map("serialize", $directeurs)));

                $facettes = [
                    [
                        'id' => 'type-document',
                        'data' => [
                            [
                                'id' => 'memoire-all',
                                'valeur' => 'Mémoire',
                                'nbre' => $nbreMemoire,
                                'isChecked' => false,
                                'slug' => 'memoire-all',
                            ]
                        ],
                        'nom' => 'Type de document',
                        'type' => '',
                        'colonne' => 'all',
                        'index' => 0,
                        'icon' => 'ti-files',
                    ],
                    [
                        'id' => 'auteur',
                        'data' => $auteurs,
                        'nom' => 'Auteur',
                        'type' => 'document',
                        'colonne' => 'auteur',
                        'index' => 1,
                        'icon' => 'ti-id-badge',
                    ],
                    [
                        'id' => 'directeur',
                        'data' => $directeurs,
                        'nom' => 'Directeur de mémoire',
                        'type' => 'document',
                        'colonne' => 'directeur',
                        'index' => 2,
                        'icon' => 'ti-user',
                    ],
                    [
                        'id' => 'domaine-formation',
                        'data' => $domaineFormations,
                        'nom' => 'Domaine de formation',
                        'type' => 'memoire',
                        'colonne' => 'domaine_formation',
                        'index' => 3,
                        'icon' => 'ti-book'
                    ],
                    [
                        'id' => 'niveau-etude',
                        'data' => $niveauEtudes,
                        'nom' => 'Niveau d\'étude',
                        'type' => 'memoire',
                        'colonne' => 'niveau_etude',
                        'index' => 4,
                        // 'icon' => 'ti-bookmark-alt'
                        'icon' => 'ti-medall-alt'
                    ],
                    [
                        'id' => 'site-catalogage',
                        'data' => $siteCatalogues,
                        'nom' => 'Site de catalogage',
                        'type' => 'document',
                        'colonne' => 'site_catalogage',
                        'index' => 5,
                        'icon' => 'ti-bookmark-alt'
                    ],
                    [
                        'id' => 'filiere',
                        'data' => $filiers,
                        'nom' => 'Filière',
                        'type' => 'memoire',
                        'colonne' => 'filiere',
                        'index' => 6,
                        'icon' => 'ti-bookmark'
                    ],
                    [
                        'id' => 'public-cible',
                        'data' => $publicCibles,
                        'nom' => 'Public cible',
                        'type' => 'document',
                        'colonne' => 'public_cible',
                        'index' => 7,
                        'icon' => 'ti-bookmark-alt'
                    ],
                    [
                        'id' => 'sujet',
                        'data' => $sujets,
                        'nom' => 'Sujets',
                        'type' => 'ref_sujet',
                        'colonne' => 'sujet_id',
                        'index' => 8,
                        'icon' => 'ti-view-list-alt'
                        // 'icon' => 'ti-bookmark'
                    ],
                    [
                        'id' => 'type-memoire',
                        'data' => $typeMemoires,
                        'nom' => 'Type de mémoire',
                        'type' => 'memoire',
                        'colonne' => 'type_memoire',
                        'index' => 9,
                        // 'icon' => 'ti-bookmark-alt'
                        'icon' => 'ti-layout-list-thumb'
                    ],
                ];

                $nbreResultat = $documents->count();
                $paginator = $documents->paginate(15);
                $paginator->appends($request->query());

                $documents = $documents->get()->map(function ($document) {
                    $document->favori = false;
                    if (Auth::check()) {
                        $favoris = Favoris::where('user_id', auth()->user()->id)
                            ->where('document_id', $document->id)
                            ->first();
                        if ($favoris) {
                            $document->favori = true;
                        }
                    }
                    return $document;
                });

                $documents = $documents->take(15);

                break;
            default:
                break;
        }

        // dd($facettes);

        $filtres_list = $filtres;
        $filtres = json_encode($filtres);
        //dd($documents);
        return view('client.search-list', compact(
            'documents',
            'sexes',
            'cle',
            'type',
            'nbreMemoire',
            'nbreResultat',
            'typeMemoires',
            'paginator',
            'facettes',
            'filtres',
            'filtres_list'
        ));
    }

    public static function slugify($chaine)
    {
        $chaine = strtolower(trim(preg_replace('/[^a-zA-Z0-9]+/', '-', $chaine), '-'));
        return $chaine;
    }

    public function baseFilter($type, $cle)
    {
        $documents = [];
        switch ($type) {
            case 'all':
                $documents = Document::with('memoire', 'image', 'ref_sujet')
                    ->where(function ($query) use ($cle) {
                        $query->where('is_public', 1)
                            ->where(function ($query) use ($cle) {
                                $query->where('titre', 'like', '%' . $cle . '%')
                                    ->orWhere('sous_titre', 'like', '%' . $cle . '%')
                                    ->orWhere('auteur', 'like', '%' . $cle . '%')
                                    ->orWhereHas('memoire', function ($query) use ($cle) {
                                        $query->where('specialite', 'like', '%' . $cle . '%');
                                    })
                                    ->orWhereHas('memoire', function ($query) use ($cle) {
                                        $query->where('mot_cle', 'like', '%' . $cle . '%');
                                    })
                                    ->orWhere('code', 'like', '%' . $cle . '%')
                                    ->orWhere('url', 'like', '%' . $cle . '%')
                                    ->orWhere('categorie', 'like', '%' . $cle . '%');
                            });
                    });
                break;
            case 'memoire':
                break;
            default:
                break;
        }
        return $documents;
    }


    public function detailMemoire($id)
    {
        $type = 'all';
        $cle = '';
        $sexes = Reference::where('type', 'Comptes')->where('nom', 'Sexe')->first();
        $document = Document::with('memoire', 'fichiers')->find($id);
        $document->increment('nbre_consultation');
        return view('client.search-show', compact('document', 'sexes', 'type', 'cle'));
    }

    /* public function dowloadpdf()
    {
        $dompdf = new Dompdf();
        $html = view('client.pdfsearch')->render();
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();
        $dompdf->stream('document.pdf', ['Attachment' => true]);
    } */

    public function downloadExcel()
    {
        $data = DB::table('users')->get();
        return Excel::download(new DataTableExport($data), 'data.xlsx');
    }

    /*      public function downloadPDF()
        {
            $data = DB::table('users')->get();
            $pdf = PDF::loadView('pdf', compact('data'));
            return $pdf->download('data.pdf');
        } */
    public function dowloadpdf($filename)
    {
        $type = request()->type_document;
        $cle = request()->mot_cle;
        $data = [];
                $data = Document::with('memoire', 'image', 'ref_sujet')
                    ->where(function ($query) use ($cle) {
                        $query->where('is_public', 1)
                            ->where(function ($query) use ($cle) {
                                $query->where('titre', 'like', '%' . $cle . '%')
                                    ->orWhere('sous_titre', 'like', '%' . $cle . '%')
                                    ->orWhere('auteur', 'like', '%' . $cle . '%')
                                    ->orWhereHas('memoire', function ($query) use ($cle) {
                                        $query->where('specialite', 'like', '%' . $cle . '%');
                                    })
                                    ->orWhereHas('memoire', function ($query) use ($cle) {
                                        $query->where('mot_cle', 'like', '%' . $cle . '%');
                                    })
                                    ->orWhere('code', 'like', '%' . $cle . '%')
                                    ->orWhere('url', 'like', '%' . $cle . '%')
                                    ->orWhere('categorie', 'like', '%' . $cle . '%');
                            });
                    });
                //dd($data);




        if($filename == "pdf")
        {
        }
         // Récupérer les données du DataTable
         $name = "facture_".date('Y-m-d_H-i-s', strtotime(now())).".pdf";
        // Générer le PDF
        // $pdf = Pdf::loadView('pdf', compact('data'))
        $pdf = Pdf::loadView('client.print', compact('data'))

         ->setPaper('a4')
        ->save(public_path("pdf/".$name))
        ;
       // dd('aze');

        return $pdf->download($name);
    }

    public function sendEmaill(Request $request)
    {
        $option = $request->input('option');
        $data = DB::table('users')->get();
        $pdf = Pdf::loadView('pdf', ['data' => $data]);
        $excel = Excel::create('data', function ($excel) use ($data) {
            $excel->sheet('Sheet 1', function ($sheet) use ($data) {
                $sheet->fromArray($data);
            });
        })->export('xlsx');
        Mail::send('email', ['data' => $data], function ($message) use ($pdf, $excel, $option) {
            $message->to('<EMAIL>');
            $message->subject('Data');
            if ($option == 'pdf') {
                $message->attachData($pdf->output(), 'data.pdf');
            } else if ($option == 'excel') {
                $message->attachData($excel, 'data.xlsx');
            }
        });
        return response()->json(['message' => 'Email sent']);
    }
    public function sendEmail()
    {
        $data = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ];

        Mail::send('pdf', $data, function ($message) {
            $message->to('<EMAIL>')
                ->subject('Exemple d\'e-mail en Laravel');
        });

        return 'E-mail envoyé avec succès';
    }

    public function exportPDF(Request $request)
    {
        $documents = json_decode($request->data);
        $name = "facture_".date('Y-m-d_H-i-s', strtotime(now())).".pdf";
        // Générer le PDF
        // $pdf = Pdf::loadView('pdf', compact('documents'))
        $pdf = Pdf::loadView('client.print', compact('documents'))

         ->setPaper('a4')
        ->save(public_path("pdf/".$name))
        ;
       // dd('aze');

        return $pdf->download($name);
    }
    public function envoiMail(Request $request)
    {
        $documents = json_decode($request->data);

        // Code pour envoyer le courrier électronique avec les données
        Mail::send('pdf', ['documents' => $documents], function ($message) {
            $message->to('<EMAIL>')
                    ->subject('Sujet du courrier électronique');
        });
        // Exemple d'utilisation de la classe Mail de Laravel
        Mail::to('<EMAIL>')->send(new VotreMail($documents));

        return response()->json(['message' => 'Courrier électronique envoyé avec succès']);
    }
    public function effectuerAction(Request $request)
    {
        $action = $request->action;

        if ($action === 'imprimer') {
            return $this->imprimer();
        } elseif ($action === 'envoi-mail') {
            return $this->envoiMail();
        } elseif ($action === 'export-pdf') {
            return $this->exportPDF();
        }

        // Autre logique ou redirection si nécessaire
    }
}
