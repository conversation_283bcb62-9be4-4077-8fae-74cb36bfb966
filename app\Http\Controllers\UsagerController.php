<?php

namespace App\Http\Controllers;

use App\Models\Compte;
use App\Models\Reference;
use App\Models\Usager;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UsagerController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $sexes = Reference::where('type', 'Comptes')->where('nom', 'Sexe')->first();
        $cartes = Reference::where('type', 'Comptes')->where('nom', 'Type de carte')->first();
        $classes = Reference::where('type', 'Comptes')->where('nom', 'Classe')->first();
        $groupes = Reference::where('type', 'Comptes')->where('nom', 'Groupe')->first();
        $services = Reference::where('type', 'Comptes')->where('nom', 'Service')->first();
        $villes = Reference::where('type', 'Comptes')->where('nom', 'Ville')->first();
        $sites = Reference::where('type', 'Document')->where('nom', 'Institut')->first();
        $classes = Reference::where('type', 'Comptes')->where('nom', 'Classe')->first();
        $filieres = Reference::where('type', 'Document')->where('nom', 'Filière')->first();
        
        return view('admin.compte-add-usr', compact('sexes', 'cartes', 'classes', 'groupes', 'services', 'villes', 'sites', 'classes', 'filieres'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Verifier la saisie du nom, prenom, sexe, type_role, username, password
        $request->validate([
            'nom' => 'required',
            'prenom' => 'required',
            'sexe' => 'required',
            'username' => 'required',
            'mot_de_passe' => 'required',
        ]);

        $registration = $request->registration;

        // Verifier si le username existe deja
        $user = User::where('username', $request->username)->first();
        if ($user) {
            return back()->with('error', 'Ce nom d\'utilisateur existe deja');
        }

        // Verifier si l'email existe deja
        if ($request->email != '') {
            $user = User::where('email', $request->email)->first();
            if ($user) {
                return back()->with('error', 'Cet email existe deja');
            }
        }

        //Verifier si  le pseudo existe deja
        if ($request->pseudo != '') {
            $usager = Usager::where('pseudo', $request->pseudo)->first();
            if ($usager) {
                return back()->with('error', 'Ce pseudo existe deja');
            }
        }

        // Verifier si la combinaison nom et prenom existe deja
        $user = User::where('nom', $request->nom)->where('prenom', $request->prenom)->first();
        if ($user) {
            return back()->with('error', 'Ce nom et prenom existe deja');
        }

        DB::beginTransaction();

        try {
            // Enregistrer les donnees dans la table user
            $user = new User();
            $user->nom = $request->nom;
            $user->prenom = $request->prenom;
            $user->email = $request->email;
            if ($registration == 'admin') {
                $user->is_active = intval($request->actif);
            }
            $user->username = $request->username;
            $user->password = bcrypt($request->mot_de_passe);
            $user->is_admin = 0;
            $user->save();

            // Enregistrer les donnees dans la table compte
            $compte = new Compte();
            $compte->sexe = $request->sexe;
            $compte->telephone = $request->telephone;
            if ($registration == 'admin') {
                $compte->date_naissance = $request->date_naissance;
                $compte->groupe = $request->groupe;
                $compte->adresse = $request->adresse;
                $compte->ville = $request->ville;
                $compte->commentaire = $request->commentaire;
            }
            $compte->user_id = $user->id;
            $compte->save();

            // Enregistrer les donnees dans la table usager
            $usager = new Usager();
            if ($registration == 'admin') {
                $usager->inscrit_a = $request->inscrit_a;
                $usager->pseudo = $request->pseudo;
                $usager->type_carte = $request->type_carte;
                $usager->numero_carte = $request->numero_carte;
                $usager->classe = $request->classe;
                $usager->filiere = $request->filiere;
                $usager->message = $request->message;
            }
            $usager->compte_id = $compte->id;
            $usager->save();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Erreur lors de l\'enregistrement');
        }

        if ($registration == 'admin') {
            return back()->with('success', 'Compte usager enregistre avec succes');
        }

        // Transform $request->mot_de_passe to $request->password
        $request->merge(['password' => $request->mot_de_passe]);

        return AuthenticationController::login($request);
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\usager  $usager
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $compte = Compte::find($id);
        // Verifier si le compte existe
        if (!$compte) {
            return back()->with('error', 'Ce compte n\'existe pas');
        }
        return view('admin.compte-show-usr', compact('compte'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  $id
     * @return \Illuminate\Http\Response
     */

    public function edit($id)
    {
        $sexes = Reference::where('type', 'Comptes')->where('nom', 'Sexe')->first();
        $cartes = Reference::where('type', 'Comptes')->where('nom', 'Type de carte')->first();
        $classes = Reference::where('type', 'Comptes')->where('nom', 'Classe')->first();
        $groupes = Reference::where('type', 'Comptes')->where('nom', 'Groupe')->first();
        $services = Reference::where('type', 'Comptes')->where('nom', 'Service')->first();
        $villes = Reference::where('type', 'Comptes')->where('nom', 'Ville')->first();
        $sites = Reference::where('type', 'Document')->where('nom', 'Institut')->first();
        $classes = Reference::where('type', 'Comptes')->where('nom', 'Classe')->first();
        $filieres = Reference::where('type', 'Document')->where('nom', 'Filière')->first();
        $compte = Compte::find($id);

        return view('admin.compte-edit-usr', compact(
            'compte',
            'sexes',
            'classes',
            'groupes',
            'services',
            'villes',
            'sites',
            'cartes',
            'filieres'
        ));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\usager  $usager
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // Verifier la saisie du nom, prenom, sexe, username
        $request->validate([
            'nom' => 'required',
            'prenom' => 'required',
            'sexe' => 'required',
            'username' => 'required',
        ]);

        // Verifier si le username a ete modifié
        $compte = Compte::find($id);
        $user = User::find($compte->user_id);
        $usager = Usager::where('compte_id', $compte->id)->first();

        if ($user->username != $request->username) {
            // Verifier si le username existe deja
            $user = User::where('username', $request->username)->first();
            if ($user) {
                return back()->with('error', 'Ce nom d\'utilisateur existe deja');
            }
        }

        // Verifier si l'email existe deja
        if ($request->email != '' && $request->email != $user->email) {
            $user = User::where('email', $request->email)->first();
            if ($user) {
                return back()->with('error', 'Cet email existe deja');
            }
        }

        // Verifier si  le pseudo existe deja
        if ($request->pseudo != '' && $request->pseudo != $usager->pseudo) {
            $usager = Usager::where('pseudo', $request->pseudo)->first();
            if ($usager) {
                return back()->with('error', 'Ce pseudo existe deja');
            }
        }

        // Verifier si la combinaison nom et prenom existe deja
        if ($request->nom != $user->nom || $request->prenom != $user->prenom) {
            $user = User::where('nom', $request->nom)->where('prenom', $request->prenom)->first();
            if ($user) {
                return back()->with('error', 'Ce nom et prenom existe deja');
            }
        }

        DB::beginTransaction();

        try {
            // Modifier les donnees dans la table user
            $compte = Compte::find($id);
            $user = User::find($compte->user_id);
            $usager = Usager::where('compte_id', $compte->id)->first();

            $user->nom = $request->nom;
            $user->prenom = $request->prenom;
            $user->email = $request->email;
            $user->is_active = intval($request->actif);
            $user->username = $request->username;
            $user->save();

            // Enregistrer les donnees dans la table compte
            $compte->date_naissance = $request->date_naissance;
            $compte->groupe = $request->groupe;
            $compte->telephone = $request->telephone;
            $compte->adresse = $request->adresse;
            $compte->ville = $request->ville;
            $compte->commentaire = $request->commentaire;
            $compte->sexe = $request->sexe;
            $compte->save();

            // Enregistrer les donnees dans la table usager
            $usager->inscrit_a = $request->inscrit_a;
            $usager->pseudo = $request->pseudo;
            $usager->type_carte = $request->type_carte;
            $usager->numero_carte = $request->numero_carte;
            $usager->classe = $request->classe;
            $usager->filiere = $request->filiere;
            $usager->message = $request->message;
            $usager->save();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Erreur lors de la modification');
        }

        return redirect()->route('comptes.index')->with('success', 'Compte usager modifié avec succes');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\usager  $usager
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $data = [];

        // Recuperer le compte selon l'id s'il existe
        $compte = Compte::find($id);
        $usager = Usager::where('compte_id', $id)->first();
        if (!$compte || !$usager) {
            // Assign error message
            $data['success'] = false;
            $data['message'] = 'Compte introuvable';
            return response()->json($data);
        }

        // Supprimer le compte
        $compte->delete();
        $usager->delete();

        // Assign success message
        $data['success'] = true;
        $data['message'] = 'Compte supprimé avec succès';

        return response()->json($data);
    }
}
