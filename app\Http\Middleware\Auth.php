<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;

class Auth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        if(auth()->guest())
        {
            // Enregistrer l'url courant dans la session
            $request->session()->put('url.intended', $request->url());

            return Redirect::route('welcome')->withErrors([
                'username' => 'Veuillez vous connecter pour poursuivre.',
            ]);
        }
        return $next($request);
    }
}
