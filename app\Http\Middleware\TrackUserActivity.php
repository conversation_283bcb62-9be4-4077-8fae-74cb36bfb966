<?php

namespace App\Http\Middleware;

use App\Models\Session as ModelsSession;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;

class TrackUserActivity
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle($request, Closure $next)
    {
        // Savoir si l'utilisateur est connecté dans la partie admin
        $currentUrl = $request->url();
        $currentSession = ModelsSession::where('id', Session::getId())->first();
        if ($currentSession == null) {
            return $next($request);
        }
        if (strpos($currentUrl, '/staff/') !== false) {
            $currentSession->is_public = false;
        } else {
            $currentSession->is_public = true;
        }
        $currentSession->save();
        return $next($request);
    }

}
