<?php

namespace App\Imports;

use App\Models\Document;
use App\Models\Memoire;
use App\Models\Reference;
use App\Models\ReferenceValeur;
use App\Models\SujetValeur;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToCollection;

class MemoireImport implements ToCollection
{
    private $count = 0;
    private $typeDocument = '';
    private $statut = 0;

    public function __construct($typeDocument)
    {
        $this->typeDocument = $typeDocument;
    }

    public function collection($rows)
    {
        foreach ($rows as $key => $row) {
            if ($key == 0) {
                if ($this->typeDocument == $row['0']) {
                    continue;
                } else {
                    $this->statut = -1;
                    break;
                }
            }
            if ($key == 1) {
                continue;
            }

            $tmp = [];

            $tmp['titre'] = $row['0'];
            $tmp['sous_titre'] = $row['1'];
            $tmp['auteur'] = $row['2'];
            $tmp['sujet'] = $row['3'];
            $tmp['pays_publication'] = $row['4'];
            $tmp['resume'] = $row['5'];
            $tmp['annee_publication'] = $row['6'];
            $tmp['directeur'] = $row['7'];
            $tmp['langue'] = $row['8'];
            $tmp['code'] = $row['9'];
            $tmp['url'] = $row['10'];
            $tmp['note'] = $row['11'];
            $tmp['theme'] = $row['12'];
            $tmp['autorisation'] = $row['13'];
            $tmp['materiel_accompagnement'] = $row['14'];
            $tmp['site_catalogage'] = $row['15'];
            $tmp['public_cible'] = $row['16'];
            $tmp['categorie'] = $row['17'];
            $tmp['is_public'] = $row['18'];
            $tmp['nom_image'] = $row['19'];
            $tmp['fonction_auteur'] = $row['20'];
            $tmp['niveau_etude'] = $row['21'];
            $tmp['institut'] = $row['22'];
            $tmp['date_soutenance'] = $row['23'];
            $tmp['date_soutenance'] = date('Y-m-d', ($tmp['date_soutenance'] - 25569) * 86400);
            $tmp['domaine_formation'] = $row['24'];
            $tmp['specialite'] = $row['25'];
            $tmp['type_memoire'] = $row['26'];
            $tmp['filiere'] = $row['27'];
            $tmp['mot_cles'] = $row['28'];

            // Verifier si un document avec le meme titre, sous titre et auteur existe
            $document = Document::where('titre', $tmp['titre'])->where('sous_titre', $tmp['sous_titre'])->where('auteur', $tmp['auteur'])->count();
            if ($document > 0) {
                continue;
            }

            DB::beginTransaction();

            try {
                // Verification des references
                if ($tmp['pays_publication']) {
                    $ref = Reference::where('type', 'Document')->where('nom', 'Pays de publication')->first();
                    $ref_check_tmp = ReferenceValeur::where('reference_id', $ref->id)->where('valeur', $tmp['pays_publication'])->first();
                    if (!$ref_check_tmp) {
                        $ref_check_tmp = new ReferenceValeur();
                        $ref_check_tmp->reference_id = $ref->id;
                        $ref_check_tmp->valeur = $tmp['pays_publication'];
                        $ref_check_tmp->save();
                    }
                    $tmp['pays_publication'] = $ref_check_tmp->id;
                }

                if ($tmp['niveau_etude']) {
                    $ref = Reference::where('type', 'Document')->where('nom', "Niveau d'étude")->first();
                    $ref_check_tmp = ReferenceValeur::where('reference_id', $ref->id)->where('valeur', $tmp['niveau_etude'])->first();
                    if (!$ref_check_tmp) {
                        $ref_check_tmp = new ReferenceValeur();
                        $ref_check_tmp->reference_id = $ref->id;
                        $ref_check_tmp->valeur = $tmp['niveau_etude'];
                        $ref_check_tmp->save();
                    }
                    $tmp['niveau_etude'] = $ref_check_tmp->id;
                }

                if ($tmp['institut']) {
                    $ref = Reference::where('type', 'Document')->where('nom', 'Institut')->first();
                    $ref_check_tmp = ReferenceValeur::where('reference_id', $ref->id)->where('valeur', $tmp['institut'])->first();
                    if (!$ref_check_tmp) {
                        $ref_check_tmp = new ReferenceValeur();
                        $ref_check_tmp->reference_id = $ref->id;
                        $ref_check_tmp->valeur = $tmp['institut'];
                        $ref_check_tmp->save();
                    }
                    $tmp['institut'] = $ref_check_tmp->id;
                }

                if ($tmp['langue']) {
                    $ref = Reference::where('type', 'Document')->where('nom', 'Langue')->first();
                    $ref_check_tmp = ReferenceValeur::where('reference_id', $ref->id)->where('valeur', $tmp['langue'])->first();
                    if (!$ref_check_tmp) {
                        $ref_check_tmp = new ReferenceValeur();
                        $ref_check_tmp->reference_id = $ref->id;
                        $ref_check_tmp->valeur = $tmp['langue'];
                        $ref_check_tmp->save();
                    }
                    $tmp['langue'] = $ref_check_tmp->id;
                }

                if ($tmp['filiere']) {
                    $ref = Reference::where('type', 'Document')->where('nom', 'Filière')->first();
                    $ref_check_tmp = ReferenceValeur::where('reference_id', $ref->id)->where('valeur', $tmp['filiere'])->first();
                    if (!$ref_check_tmp) {
                        $ref_check_tmp = new ReferenceValeur();
                        $ref_check_tmp->reference_id = $ref->id;
                        $ref_check_tmp->valeur = $tmp['filiere'];
                        $ref_check_tmp->save();
                    }
                    $tmp['filiere'] = $ref_check_tmp->id;
                }

                if ($tmp['site_catalogage']) {
                    $ref = Reference::where('type', 'Document')->where('nom', 'Site de catalogage')->first();
                    $ref_check_tmp = ReferenceValeur::where('reference_id', $ref->id)->where('valeur', $tmp['site_catalogage'])->first();
                    if (!$ref_check_tmp) {
                        $ref_check_tmp = new ReferenceValeur();
                        $ref_check_tmp->reference_id = $ref->id;
                        $ref_check_tmp->valeur = $tmp['site_catalogage'];
                        $ref_check_tmp->save();
                    }
                    $tmp['site_catalogage'] = $ref_check_tmp->id;
                }

                if ($tmp['public_cible']) {
                    $ref = Reference::where('type', 'Document')->where('nom', 'Public visé')->first();
                    $ref_check_tmp = ReferenceValeur::where('reference_id', $ref->id)->where('valeur', $tmp['public_cible'])->first();
                    if (!$ref_check_tmp) {
                        $ref_check_tmp = new ReferenceValeur();
                        $ref_check_tmp->reference_id = $ref->id;
                        $ref_check_tmp->valeur = $tmp['public_cible'];
                        $ref_check_tmp->save();
                    }
                    $tmp['public_cible'] = $ref_check_tmp->id;
                }

                if ($tmp['categorie']) {
                    $ref = Reference::where('type', 'Document')->where('nom', 'Public visé')->first();
                    $ref_check_tmp = ReferenceValeur::where('reference_id', $ref->id)->where('valeur', $tmp['categorie'])->first();
                    if (!$ref_check_tmp) {
                        $ref_check_tmp = new ReferenceValeur();
                        $ref_check_tmp->reference_id = $ref->id;
                        $ref_check_tmp->valeur = $tmp['categorie'];
                        $ref_check_tmp->save();
                    }
                    $tmp['categorie'] = $ref_check_tmp->id;
                }

                if ($tmp['sujet']) {
                    $tmp['sujet'] = explode(',', $tmp['sujet']);
                    $tmp['sujet'] = array_unique($tmp['sujet']);
                    
                    foreach ($tmp['sujet'] as $key => $value) {
                        $value = trim($value);
                        $ref = Reference::where('type', 'Document')->where('nom', 'Sujet')->first();
                        $ref_check_tmp = ReferenceValeur::where('reference_id', $ref->id)->where('valeur', $value)->first();
                        if (!$ref_check_tmp) {
                            $ref_check_tmp = new ReferenceValeur();
                            $ref_check_tmp->reference_id = $ref->id;
                            $ref_check_tmp->valeur = $value;
                            $ref_check_tmp->save();
                        }
                        $tmp['sujet'][$key] = $ref_check_tmp->id;
                    }
                }

                if ($tmp['type_memoire']) {
                    $ref = Reference::where('type', 'Document')->where('nom', 'Type de mémoire')->first();
                    $ref_check_tmp = ReferenceValeur::where('reference_id', $ref->id)->where('valeur', $tmp['type_memoire'])->first();
                    if (!$ref_check_tmp) {
                        $ref_check_tmp = new ReferenceValeur();
                        $ref_check_tmp->reference_id = $ref->id;
                        $ref_check_tmp->valeur = $tmp['type_memoire'];
                        $ref_check_tmp->save();
                    }
                    $tmp['type_memoire'] = $ref_check_tmp->id;
                }

                $document = new Document();
                $document->titre = $tmp['titre'];
                $document->sous_titre = $tmp['sous_titre'];
                $document->auteur = $tmp['auteur'];
                $document->pays_publication = $tmp['pays_publication'];
                $document->resume = $tmp['resume'];
                $document->annee_publication = $tmp['annee_publication'];
                $document->directeur = $tmp['directeur'];
                $document->langue = $tmp['langue'];
                $document->code = $tmp['code'];
                $document->url = $tmp['url'];
                $document->note = $tmp['note'];
                $document->autorisation = $tmp['autorisation'];
                $document->materiel_accompagnement = $tmp['materiel_accompagnement'];
                $document->categorie = $tmp['categorie'];
                $document->public_cible = $tmp['public_cible'];
                $document->site_catalogage = $tmp['site_catalogage'];
                $document->public_cible = $tmp['public_cible'];
                $document->is_public = intval($tmp['is_public']);
                $document->save();

                if ($tmp['sujet']) {
                    foreach ($tmp['sujet'] as $key => $value) {
                        $sujetvaleur = new SujetValeur();
                        $sujetvaleur->document_id = $document->id;
                        $sujetvaleur->sujet_id = $value;
                        $sujetvaleur->save();
                    }
                }

                $memoire = new Memoire();
                $memoire->document_id = $document->id;
                $memoire->fonction_auteur = $tmp['fonction_auteur'];
                $memoire->niveau_etude = $tmp['niveau_etude'];
                $memoire->institut = $tmp['institut'];
                $memoire->date_soutenance = $tmp['date_soutenance'];

                // if ($tmp['date_soutenance'] != null) {
                //     $date_tmp = Carbon::createFromFormat('d/m/Y', $tmp['date_soutenance'])->format('Y-m-d');
                //     if ($date_tmp) {
                //         $memoire->date_soutenance = $date_tmp;
                //     }
                // }
                $memoire->domaine_formation = $tmp['domaine_formation'];
                $memoire->specialite = $tmp['specialite'];
                $memoire->type_memoire = $tmp['type_memoire'];
                $memoire->filiere = $tmp['filiere'];
                $memoire->mot_cle = $tmp['mot_cles'];
                $memoire->save();

                $this->count++;

                DB::commit();
            } catch (\Exception $e) {
                DB::rollback();
                return redirect()->back()->with('error', 'Une erreur est survenue');
            }
        }
    }

    public function getCount()
    {
        return $this->count;
    }

    public function getStatut()
    {
        return $this->statut;
    }
}
