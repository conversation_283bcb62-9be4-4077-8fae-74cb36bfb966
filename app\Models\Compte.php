<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;
use <PERSON><PERSON><PERSON>\Purify\Casts\PurifyHtmlOnGet;

class Compte extends Model
{
    use SoftDeletes;
    use HasFactory;
    use Userstamps;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */

    protected $fillable = [
        'sexe',
        'date_naissance',
        'groupe',
        'telephone',
        'adresse',
        'ville',
        'commentaire',
    ];

    protected $casts = [
        'sexe' => PurifyHtmlOnGet::class,
        'date_naissance' => PurifyHtmlOnGet::class,
        'telephone' => PurifyHtmlOnGet::class,
        'adresse' => PurifyHtmlOnGet::class,
        'commentaire' => PurifyHtmlOnGet::class,
    ];

    public function professionnel()
    {
        return $this->hasOne(Professionnel::class);
    }

    public function usager()
    {
        return $this->hasOne(Usager::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }



    public function ref_groupe()
    {
        return $this->belongsTo(ReferenceValeur::class, 'groupe', 'id');
    }

    public function ref_ville()
    {
        return $this->belongsTo(ReferenceValeur::class, 'ville', 'id');
    }

}
