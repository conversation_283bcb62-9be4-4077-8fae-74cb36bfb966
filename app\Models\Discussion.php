<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;
use <PERSON><PERSON><PERSON>\Purify\Casts\PurifyHtmlOnGet;


class Discussion extends Model
{
    use HasFactory;
    use SoftDeletes;
    use Userstamps;

    protected $fillable = [
        'message_id',
        'message',
        'sent_by_admin'
    ];

    protected $casts = [
        'message' => PurifyHtmlOnGet::class,
    ];

    public function message()
    {
        return $this->belongsTo(Message::class);
    }
}
