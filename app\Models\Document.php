<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;
use <PERSON><PERSON><PERSON>\Purify\Casts\PurifyHtmlOnGet;


class Document extends Model
{
    use HasFactory;
    use SoftDeletes;
    use Userstamps;

    protected $fillable = [
        'titre',
        'sous_titre',
        'auteur',
        'pays_publication',
        'resume',
        'annee_publication',
        'directeur',
        'langue',
        'code',
        'url',
        'note',
        // 'sujet',
        'theme',
        'nbr_page',
        'autorisation',
        'materiel_accompagnement',
        'site_catalogage',
        'public_cible',
        'categorie',
        'is_public',
        'image_id',
    ];

    protected $casts = [
        'titre' => PurifyHtmlOnGet::class,
        'sous_titre' => PurifyHtmlOnGet::class,
        'auteur' => PurifyHtmlOnGet::class,
        'resume' => PurifyHtmlOnGet::class,
        'annee_publication' => PurifyHtmlOnGet::class,
        'directeur' => PurifyHtmlOnGet::class,
        'code' => PurifyHtmlOnGet::class,
        'url' => PurifyHtmlOnGet::class,
        'note' => PurifyHtmlOnGet::class,
        'theme' => PurifyHtmlOnGet::class,
    ];


    // hasOne memoire
    public function memoire()
    {
        return $this->hasOne(Memoire::class);
    }

    public function ref_langue()
    {
        return $this->belongsTo(ReferenceValeur::class, 'langue', 'id');
    }

    // hasMany vers SujetValeur
    public function ref_sujet()
    {
        return $this->hasMany(SujetValeur::class);
    }

    // belongsTo theme
    public function ref_theme()
    {
        return $this->belongsTo(ReferenceValeur::class, 'theme', 'id');
    }

    public function ref_site_catalogage()
    {
        return $this->belongsTo(ReferenceValeur::class, 'site_catalogage', 'id');
    }

    public function ref_categorie()
    {
        return $this->belongsTo(ReferenceValeur::class, 'categorie', 'id');
    }

    public function ref_public_cible()
    {
        return $this->belongsTo(ReferenceValeur::class, 'public_cible', 'id');
    }

    public function ref_pays_publication()
    {
        return $this->belongsTo(ReferenceValeur::class, 'pays_publication', 'id');
    }

    // FICHIER
    public function fichiers()
    {
        return $this->hasMany(DocumentFichier::class);
    }

    // IMAGE de couverture
    public function image()
    {
        return $this->belongsTo(Fichier::class, 'image_id', 'id');
    }

    // FAVORIS
    public function favoris()
    {
        return $this->hasMany(Favoris::class);
    }
}
