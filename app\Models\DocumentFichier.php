<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;

class DocumentFichier extends Model
{
    use SoftDeletes;
    use HasFactory;
    use Userstamps;

    protected $fillable = [
        'document_id',
        'fichier_id',
    ];

    // belongsTo document
    public function document()
    {
        return $this->belongsTo(Document::class);
    }

    // belongsTo fichier
    public function fichier()
    {
        return $this->belongsTo(Fichier::class);
    }
}

