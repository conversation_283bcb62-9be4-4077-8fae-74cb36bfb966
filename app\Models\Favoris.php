<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;
use <PERSON><PERSON><PERSON>\Purify\Casts\PurifyHtmlOnGet;

class Favoris extends Model
{
    use HasFactory;
    use SoftDeletes;
    use Userstamps;

    protected $fillable = [
        'user_id',
        'document_id',
    ];

    // belongsTo user
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // belongsTo document
    public function document()
    {
        return $this->belongsTo(Document::class);
    }
}
