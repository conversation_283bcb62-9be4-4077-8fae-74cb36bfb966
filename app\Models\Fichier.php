<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;

class Fichier extends Model
{
    use SoftDeletes;
    use HasFactory;
    use Userstamps;

    protected $fillable = [
        'nom',
        'chemin',
    ];

    // hasOne document_fichier
    public function document_fichier()
    {
        return $this->hasOne(DocumentFichier::class);
    }

    // hasOne user
    public function user_photo()
    {
        return $this->hasOne(User::class, 'photo_id', 'id');
    }

    // hasOne document image
    public function document_image()
    {
        return $this->hasOne(Document::class, 'image_id', 'id');
    }


    
}
