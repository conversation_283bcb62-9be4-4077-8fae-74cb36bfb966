<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;
use <PERSON><PERSON><PERSON>\Purify\Casts\PurifyHtmlOnGet;

class Memoire extends Model
{
    use SoftDeletes;
    use HasFactory;
    use Userstamps;


    protected $fillable = [
        'fonction_auteur',
        'niveau_etude',
        'institut',
        'date_soutenance',
        'domaine_formation',
        'specialite',
        'type_memoire',
        'filiere',
        'mot_cle',
    ];

    protected $casts = [
        'fonction_auteur' => PurifyHtmlOnGet::class,
        'date_soutenance' => PurifyHtmlOnGet::class,
        'domaine_formation' => PurifyHtmlOnGet::class,
        'specialite' => PurifyHtmlOnGet::class,
        'mot_cle' => PurifyHtmlOnGet::class,
    ];


    // belongsTo document
    public function document()
    {
        return $this->belongsTo(Document::class);
    }

    public function ref_niveau_etude()
    {
        return $this->belongsTo(ReferenceValeur::class, 'niveau_etude', 'id');
    }

    public function ref_institut()
    {
        return $this->belongsTo(ReferenceValeur::class, 'institut', 'id');
    }

    public function ref_type_memoire()
    {
        return $this->belongsTo(ReferenceValeur::class, 'type_memoire', 'id');
    }

    public function ref_filiere()
    {
        return $this->belongsTo(ReferenceValeur::class, 'filiere', 'id');
    }
}
