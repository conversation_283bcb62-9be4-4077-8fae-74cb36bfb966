<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;
use <PERSON><PERSON><PERSON>\Purify\Casts\PurifyHtmlOnGet;

class Message extends Model
{
    use SoftDeletes;
    use HasFactory;
    use Userstamps;

    protected $fillable = [
        'nom',
        'prenom',
        'email',
        'telephone',
        'motif',
        'autre_motif',
        // 'message',
        'lu',
        'repondu',
        // 'reponse',
        'user_id',
        // 'sent_by_admin'
    ];

    protected $casts = [
        'nom' => PurifyHtmlOnGet::class,
        'prenom' => PurifyHtmlOnGet::class,
        'email' => PurifyHtmlOnGet::class,
        'telephone' => PurifyHtmlOnGet::class,
        'autre_motif' => PurifyHtmlOnGet::class,
        // 'message' => PurifyHtmlOnGet::class,
        // 'reponse' => PurifyHtmlOnGet::class,
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function discussions()
    {
        return $this->hasMany(Discussion::class);
    }


    // Relation avec reference
    public function getMotif()
    {
        return $this->belongsTo(ReferenceValeur::class, 'motif' , 'id');
    }
    
}
