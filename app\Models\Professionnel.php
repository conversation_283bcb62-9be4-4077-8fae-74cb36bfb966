<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;

class Professionnel extends Model
{
    use SoftDeletes;
    use HasFactory;
    use Userstamps;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */

    protected $fillable = [
        'rattache',
        'type_role',
        'service'
    ];


    public function compte()
    {
        return $this->belongsTo(Compte::class);
    }

    public function ref_service()
    {
        return $this->belongsTo(ReferenceValeur::class, 'service', 'id');
    }

    public function ref_rattache()
    {
        return $this->belongsTo(ReferenceValeur::class, 'rattache', 'id');
    }
    
}
