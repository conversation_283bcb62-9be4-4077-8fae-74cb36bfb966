<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;
use <PERSON><PERSON><PERSON>\Purify\Casts\PurifyHtmlOnGet;

class Reference extends Model
{
    use SoftDeletes;
    use HasFactory;
    use Userstamps;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */

    protected $fillable = [
        'type',
        'nom',
    ];

    protected $casts = [
        'nom' => PurifyHtmlOnGet::class,
    ];


    // Relation de one to many avec la reference_valeur
    public function reference_valeurs()
    {
        return $this->hasMany(ReferenceValeur::class);
    }
    
}
