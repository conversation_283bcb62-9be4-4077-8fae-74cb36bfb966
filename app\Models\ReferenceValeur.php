<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;
use <PERSON><PERSON><PERSON>\Purify\Casts\PurifyHtmlOnGet;

class ReferenceValeur extends Model
{
    use SoftDeletes;
    use HasFactory;
    use Userstamps;


    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */

    protected $fillable = [
        'valeur',
        'reference_id'
    ];

    protected $casts = [
        'valeur' => PurifyHtmlOnGet::class,
    ];

    // Relation de belongs to avec la reference
    public function reference()
    {
        return $this->belongsTo(Reference::class);
    } 


    // COMPTE
    public function compte_groupe()
    {
        return $this->hasOne(Compte::class, 'id', 'groupe');
    }

    public function compte_ville()
    {
        return $this->hasOne(Compte::class, 'id', 'ville');
    }

    // PROFESSIONNEL
    public function pro_service()
    {
        return $this->hasOne(Professionnel::class, 'id', 'service');
    }

    public function pro_rattache()
    {
        return $this->hasOne(Professionnel::class, 'id', 'rattache');
    }


    // USAGER
    public function usager_inscrit_a()
    {
        return $this->hasOne(Usager::class, 'id', 'inscrit_a');
    }

    public function uager_type_carte()
    {
        return $this->hasOne(Usager::class, 'id', 'type_carte');
    }

    public function usager_classe()
    {
        return $this->hasOne(Usager::class, 'id', 'classe');
    }

    public function usager_filiere()
    {
        return $this->hasOne(Usager::class, 'id', 'filiere');
    }


    // DOCUMENT
    public function document_langue()
    {
        return $this->hasOne(Document::class, 'id', 'langue');
    }

    public function document_niveau_etude()
    {
        return $this->hasOne(Document::class, 'id', 'niveau_etude');
    }

    public function document_institut()
    {
        return $this->hasOne(Document::class, 'id', 'institut');
    }

    public function document_sujet()
    {
        return $this->hasOne(Document::class, 'id', 'sujet');
    }

    public function document_theme()
    {
        return $this->hasOne(Document::class, 'id', 'theme');
    }

    public function document_type_memoire()
    {
        return $this->hasOne(Document::class, 'id', 'type_memoire');
    }

    public function document_filiere()
    {
        return $this->hasOne(Document::class, 'id', 'filiere');
    }

    public function document_site_catalogage()
    {
        return $this->hasOne(Document::class, 'id', 'site_catalogage');
    }

    public function document_categorie()
    {
        return $this->hasOne(Document::class, 'id', 'categorie');
    }

    public function document_public_cible()
    {
        return $this->hasOne(Document::class, 'id', 'public_cible');
    }

    public function document_pays_publication()
    {
        return $this->hasOne(Document::class, 'id', 'pays_publication');
    }

    // SUJET VALEUR
    public function sujet_valeurs()
    {
        return $this->hasMany(SujetValeur::class);
    }

    // MOTIF VALEUR
    public function message_motif()
    {
        return $this->hasOne(Message::class, 'id', 'motif');
    }
}
