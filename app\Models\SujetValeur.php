<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;

class SujetValeur extends Model
{
    use HasFactory;
    use SoftDeletes;
    use Userstamps;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */

    protected $fillable = [
        'sujet_id',
        'document_id'
    ];

    // Relation de belongs to avec la reference
    public function sujet()
    {
        return $this->belongsTo(ReferenceValeur::class);
    }

    // Relation de belongs to avec la document
    public function document()
    {
        return $this->belongsTo(Document::class);
    }
}
