<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Wildside\Userstamps\Userstamps;
use <PERSON><PERSON><PERSON>\Purify\Casts\PurifyHtmlOnGet;

class Usager extends Model
{
    use SoftDeletes;
    use HasFactory;
    use Userstamps;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */

    protected $fillable = [
        'inscrit_a',
        'pseudo',
        'type_carte',
        'numero_carte',
        'classe',
        'filiere',
        'message'
    ];

    protected $casts = [
        'pseudo' => PurifyHtmlOnGet::class,
        'numero_carte' => PurifyHtmlOnGet::class,
        'message' => PurifyHtmlOnGet::class,
    ];


    public function compte()
    {
        return $this->belongsTo(Compte::class);
    }

    public function ref_inscrit_a()
    {
        return $this->belongsTo(ReferenceValeur::class, 'inscrit_a', 'id');
    }

    public function ref_type_carte()
    {
        return $this->belongsTo(ReferenceValeur::class, 'type_carte', 'id');
    }

    public function ref_classe()
    {
        return $this->belongsTo(ReferenceValeur::class, 'classe', 'id');
    }

    public function ref_filiere()
    {
        return $this->belongsTo(ReferenceValeur::class, 'filiere', 'id');
    }
}
