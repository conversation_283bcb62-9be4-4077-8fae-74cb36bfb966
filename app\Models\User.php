<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Wildside\Userstamps\Userstamps;
use <PERSON><PERSON><PERSON>\Purify\Casts\PurifyHtmlOnGet;

class User extends Authenticatable
{
    use SoftDeletes;
    use HasApiTokens, HasFactory, Notifiable;
    use Userstamps;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'nom',
        'prenom',
        'username',
        'email',
        'password',
        'is_active',
        'is_admin',
        'photo_id',
    ];

    protected $casts = [
        'nom' => PurifyHtmlOnGet::class,
        'prenom' => PurifyHtmlOnGet::class,
        'username' => PurifyHtmlOnGet::class,
        'email' => PurifyHtmlOnGet::class,
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];
    
    public function photo()
    {
        return $this->belongsTo(Fichier::class, 'photo_id', 'id');
    }

    public function compte()
    {
        return $this->hasOne(Compte::class);
    }

    public function messages()
    {
        return $this->hasOne(Message::class);
    }

    public function favoris()
    {
        return $this->hasMany(Favoris::class);
    }
}
