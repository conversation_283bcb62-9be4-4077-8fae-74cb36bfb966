<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateComptesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('comptes', function (Blueprint $table) {
            $table->id();
            $table->string('sexe')->nullable();
            $table->date('date_naissance')->nullable();
            $table->unsignedBigInteger('groupe')->nullable();
            $table->foreign('groupe')->references('id')->on('reference_valeurs')->onDelete('cascade');
            $table->string('telephone')->unique()->nullable();
            $table->string('adresse')->nullable();
            $table->string('ville')->nullable();
            $table->string('commentaire')->nullable(); 
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('comptes');
    }
}
