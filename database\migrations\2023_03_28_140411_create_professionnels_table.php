<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProfessionnelsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('professionnels', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('compte_id');
            $table->foreign('compte_id')->references('id')->on('comptes')->onDelete('cascade');
            $table->unsignedBigInteger('rattache')->nullable();
            $table->foreign('rattache')->references('id')->on('reference_valeurs')->onDelete('cascade');
            $table->unsignedBigInteger('type_role');
            $table->foreign('type_role')->references('id')->on('reference_valeurs')->onDelete('cascade');
            $table->string('service')->nullable();
            $table->timestamps();
            $table->SoftDeletes();            
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('professionnels');
    }
}
