<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUsagersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('usagers', function (Blueprint $table) {
            $table->id();
            $table->string('numero_carte');
            $table->unsignedBigInteger('compte_id');
            $table->foreign('compte_id')->references('id')->on('comptes')->onDelete('cascade');
            $table->unsignedBigInteger('inscrit_a')->nullable();
            $table->foreign('inscrit_a')->references('id')->on('reference_valeurs')->onDelete('cascade');
            $table->string('pseudo');
            $table->unsignedBigInteger('type_carte')->nullable();
            $table->foreign('type_carte')->references('id')->on('reference_valeurs')->onDelete('cascade');
            $table->unsignedBigInteger('classe')->nullable();
            $table->foreign('classe')->references('id')->on('reference_valeurs')->onDelete('cascade');
            $table->unsignedBigInteger('filiere')->nullable();
            $table->foreign('filiere')->references('id')->on('reference_valeurs')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('usagers');
    }
}
