<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDocumentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('documents', function (Blueprint $table) {
            $table->id();
            $table->string('titre');
            $table->string('sous_titre')->nullable();
            $table->string('auteur');
            $table->string('pays_publication')->nullable();
            $table->text('resume')->nullable();
            $table->string('annee_publication')->nullable();
            $table->string('directeur')->nullable();
            $table->unsignedBigInteger('langue')->nullable();
            $table->foreign('langue')->references('id')->on('reference_valeurs')->onDelete('cascade');
            $table->string('code')->nullable();
            $table->string('url')->nullable();
            $table->string('note')->nullable();
            $table->unsignedBigInteger('sujet')->nullable();
            $table->foreign('sujet')->references('id')->on('reference_valeurs')->onDelete('cascade');
            $table->text('theme')->nullable();
            $table->integer('nbr_page')->nullable();
            $table->string('autorisation')->nullable();
            $table->string('materiel_accompagnement')->nullable();
            $table->unsignedBigInteger('site_catalogage')->nullable();
            $table->foreign('site_catalogage')->references('id')->on('reference_valeurs')->onDelete('cascade');
            $table->string('public_cible')->nullable();
            $table->unsignedBigInteger('categorie')->nullable();
            $table->foreign('categorie')->references('id')->on('reference_valeurs')->onDelete('cascade');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('documents');
    }
}
