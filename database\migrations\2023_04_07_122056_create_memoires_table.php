<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMemoiresTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('memoires', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('document_id');
            $table->foreign('document_id')->references('id')->on('documents')->onDelete('cascade');
            $table->string('fonction_auteur')->nullable();
            $table->unsignedBigInteger('niveau_etude')->nullable();
            $table->foreign('niveau_etude')->references('id')->on('reference_valeurs')->onDelete('cascade');
            $table->unsignedBigInteger('institut')->nullable();
            $table->foreign('institut')->references('id')->on('reference_valeurs')->onDelete('cascade');
            $table->date('date_soutenance')->nullable();
            $table->string('domaine_formation')->nullable();
            $table->string('specialite')->nullable();
            $table->unsignedBigInteger('type_memoire')->nullable();
            $table->foreign('type_memoire')->references('id')->on('reference_valeurs')->onDelete('cascade');
            $table->unsignedBigInteger('filiere')->nullable();
            $table->foreign('filiere')->references('id')->on('reference_valeurs')->onDelete('cascade');
            $table->string('mot_cle')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('memoires');
    }
}
