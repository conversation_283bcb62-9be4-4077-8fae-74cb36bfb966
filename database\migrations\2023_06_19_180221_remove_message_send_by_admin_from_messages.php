<?php

use App\Models\Message;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class RemoveMessageSendByAdminFromMessages extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('messages', function (Blueprint $table) {
            DB::statement('SET FOREIGN_KEY_CHECKS=0');
            Message::truncate();
            DB::statement('SET FOREIGN_KEY_CHECKS=1');

            $table->dropColumn('sent_by_admin');
            $table->dropColumn('reponse');
            $table->dropColumn('message');
            
            $table->boolean('closed')->default(false)->after('user_id');
            $table->boolean('closed_permanently')->default(false)->after('closed');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('messages', function (Blueprint $table) {
            //
        });
    }
}
