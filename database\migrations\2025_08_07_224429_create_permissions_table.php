<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePermissionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('permissions', function (Blueprint $table) {
            $table->id();
            $table->string('name', 191)->unique(); // Permission name (e.g., 'create_users', 'edit_documents')
            $table->string('display_name', 191)->nullable(); // Human readable name
            $table->text('description')->nullable(); // Permission description
            $table->string('module', 100)->nullable(); // Module/category (e.g., 'users', 'documents')
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('permissions');
    }
}
