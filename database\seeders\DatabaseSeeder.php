<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        // \App\Models\User::factory(10)->create();


        // Create a default user

        $user = new User();
        $user->nom = 'billson';
        $user->prenom = 'billson';
        $user->email = '<EMAIL>';
        $user->username = 'billson';
        $user->password = bcrypt('billson');
        $user->is_admin = true;
        $user->save();

        $user = new User();
        $user->nom = 'martin';
        $user->prenom = 'martin';
        $user->email = '<EMAIL>';
        $user->username = 'martin03';
        $user->password = bcrypt('martin!0');
        $user->is_admin = true;
        $user->save();

        $user = new User();
        $user->nom = 'clotilda';
        $user->prenom = 'clotilda';
        $user->email = '<EMAIL>';
        $user->username = 'clotilda';
        $user->password = bcrypt('clotilda');
        $user->is_admin = true;
        $user->save();

        // Create references
        DB::statement("
            INSERT INTO `references` (`id`, `type`, `nom`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
            (1, 'Document', 'Langue', '2023-03-26 13:20:23', '2023-03-26 13:20:23', NULL, NULL, NULL, NULL),
            (2, 'Document', 'Pays de publication', '2023-03-26 13:20:40', '2023-03-26 13:20:40', NULL, NULL, NULL, NULL),
            (3, 'Document', 'Sujet', '2023-03-26 13:20:59', '2023-03-26 13:20:59', NULL, NULL, NULL, NULL),
            (4, 'Document', 'Public visé', '2023-03-26 13:22:37', '2023-03-26 13:22:37', NULL, NULL, NULL, NULL),
            (5, 'Exemplaire', 'Site/Etablissement', '2023-03-26 13:23:03', '2023-03-26 13:23:03', NULL, NULL, NULL, NULL),
            (6, 'Document', 'Thème', '2023-03-26 13:23:17', '2023-03-26 13:23:17', NULL, NULL, NULL, NULL),
            (7, 'Document', 'Centre d\'intérêt', '2023-03-26 13:23:30', '2023-03-26 13:23:30', NULL, NULL, NULL, NULL),
            (8, 'Document', 'Genre', '2023-03-26 13:23:54', '2023-03-26 13:23:54', NULL, NULL, NULL, NULL),
            (9, 'Exemplaire', 'Localisation', '2023-03-26 13:24:08', '2023-03-26 13:24:08', NULL, NULL, NULL, NULL),
            (10, 'Exemplaire', 'Sous-localsation', '2023-03-26 13:24:25', '2023-03-26 13:24:25', NULL, NULL, NULL, NULL),
            (11, 'Exemplaire', 'Section', '2023-03-26 13:24:43', '2023-03-26 13:24:43', NULL, NULL, NULL, NULL),
            (12, 'Exemplaire', 'Emplacement', '2023-03-26 13:24:56', '2023-03-26 13:24:56', NULL, NULL, NULL, NULL),
            (13, 'Exemplaire', 'Cote', '2023-03-26 13:25:09', '2023-03-26 13:25:09', NULL, NULL, NULL, NULL),
            (14, 'Exemplaire', 'Rayon', '2023-03-26 13:25:44', '2023-03-26 13:25:44', NULL, NULL, NULL, NULL),
            (15, 'Exemplaire', 'Classement', '2023-03-26 13:26:02', '2023-03-26 13:26:02', NULL, NULL, NULL, NULL),
            (16, 'Comptes', 'Service', '2023-03-26 13:26:22', '2023-03-26 13:26:22', NULL, NULL, NULL, NULL),
            (17, 'Comptes', 'Ville', '2023-03-26 13:26:34', '2023-03-26 13:26:34', NULL, NULL, NULL, NULL),
            (18, 'Comptes', 'Type de carte', '2023-03-26 13:26:46', '2023-03-26 13:26:46', NULL, NULL, NULL, NULL),
            (19, 'Comptes', 'Groupe', '2023-03-26 13:27:01', '2023-03-26 13:27:01', NULL, NULL, NULL, NULL),
            (20, 'Comptes', 'Sexe', '2023-03-26 13:27:12', '2023-03-26 13:27:12', NULL, NULL, NULL, NULL),
            (21, 'Comptes', 'Classe', '2023-03-26 13:27:28', '2023-03-26 13:27:28', NULL, NULL, NULL, NULL),
            (22, 'Agenda', 'Accès (sur inscription)', '2023-03-26 13:28:02', '2023-03-26 13:28:02', NULL, NULL, NULL, NULL),
            (23, 'Agenda', 'Public', '2023-03-26 13:28:30', '2023-03-26 13:28:30', NULL, NULL, NULL, NULL),
            (24, 'Document', 'Niveau d\'étude', '2023-04-09 04:10:23', '2023-04-09 04:10:23', NULL, NULL, NULL, NULL),
            (25, 'Document', 'Filière', '2023-04-20 11:45:52', '2023-04-20 11:45:52', NULL, NULL, NULL, NULL),
            (26, 'Document', 'Institut', '2023-04-20 12:14:56', '2023-04-20 12:14:56', NULL, NULL, NULL, NULL),
            (27, 'Document', 'Site de catalogage', '2023-04-20 12:15:10', '2023-04-20 12:15:10', NULL, NULL, NULL, NULL),
            (28, 'Contact', 'Motif', '2023-05-10 15:46:33', '2023-05-10 15:46:33', NULL, 1, 1, NULL);
        ");

        DB::statement("
            INSERT INTO `reference_valeurs` (`id`, `valeur`, `reference_id`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `deleted_by`) VALUES
            (1, 'Français', 1, '2023-03-26 13:21:55', '2023-03-26 13:21:55', NULL, NULL, NULL, NULL),
            (2, 'Masculin', 20, '2023-04-05 16:03:27', '2023-04-05 16:03:27', NULL, NULL, NULL, NULL),
            (3, 'Féminin', 20, '2023-04-05 16:03:48', '2023-04-05 16:03:48', NULL, NULL, NULL, NULL),
            (4, 'Service commercial', 16, '2023-04-06 16:46:25', '2023-04-06 16:46:25', NULL, NULL, NULL, NULL),
            (5, 'Direction', 16, '2023-04-06 16:46:45', '2023-04-06 16:46:45', NULL, NULL, NULL, NULL),
            (6, 'Interne entreprise', 18, '2023-04-06 16:48:38', '2023-04-06 16:48:38', NULL, NULL, NULL, NULL),
            (7, 'Extérieure entreprise', 18, '2023-04-06 16:49:04', '2023-04-06 16:49:04', NULL, NULL, NULL, NULL),
            (8, 'Lomé', 17, '2023-04-06 16:49:31', '2023-04-06 16:49:31', NULL, NULL, NULL, NULL),
            (9, 'Tsévié', 17, '2023-04-06 16:50:22', '2023-04-06 16:50:22', NULL, NULL, NULL, NULL),
            (10, 'Adulte', 19, '2023-04-06 16:51:43', '2023-04-06 16:51:43', NULL, NULL, NULL, NULL),
            (11, 'Jeunes', 19, '2023-04-06 16:52:59', '2023-04-06 16:52:59', NULL, NULL, NULL, NULL),
            (12, 'Togo', 2, '2023-04-06 16:53:15', '2023-04-06 16:53:15', NULL, NULL, NULL, NULL),
            (13, 'France', 2, '2023-04-06 16:53:46', '2023-04-06 16:53:46', NULL, NULL, NULL, NULL),
            (14, 'Etudiants en Master', 4, '2023-04-06 16:54:40', '2023-04-06 16:54:40', NULL, NULL, NULL, NULL),
            (15, 'Etudiants en Licence', 4, '2023-04-06 16:55:17', '2023-04-06 16:55:17', NULL, NULL, NULL, NULL),
            (16, 'Etudiants en BTS', 4, '2023-04-06 16:55:41', '2023-04-06 16:55:41', NULL, NULL, NULL, NULL),
            (17, 'Etudiants année 1 et 2', 4, '2023-04-06 16:59:05', '2023-04-06 16:59:05', NULL, NULL, NULL, NULL),
            (18, 'Communication', 3, '2023-04-06 16:59:36', '2023-04-06 16:59:36', NULL, NULL, NULL, NULL),
            (19, 'Commerce international', 3, '2023-04-06 17:00:11', '2023-04-06 17:00:11', NULL, NULL, NULL, NULL),
            (20, 'Oui', 22, '2023-04-06 17:00:28', '2023-04-06 17:00:28', NULL, NULL, NULL, NULL),
            (21, 'Non', 22, '2023-04-06 17:00:46', '2023-04-06 17:00:46', NULL, NULL, NULL, NULL),
            (22, 'Evénement pour personnels internes', 23, '2023-04-06 17:01:17', '2023-04-06 17:01:17', NULL, NULL, NULL, NULL),
            (23, 'Evénements pour publics externes', 23, '2023-04-06 17:01:59', '2023-04-06 17:01:59', NULL, NULL, NULL, NULL),
            (24, 'Etage 1', 12, '2023-04-06 17:11:58', '2023-04-06 17:11:58', NULL, NULL, NULL, NULL),
            (25, 'RDC', 12, '2023-04-06 17:12:13', '2023-04-06 17:12:13', NULL, NULL, NULL, NULL),
            (26, 'Etage 2', 12, '2023-04-06 17:12:27', '2023-04-06 17:12:27', NULL, NULL, NULL, NULL),
            (27, 'Site de Tsévié', 9, '2023-04-06 17:12:50', '2023-04-06 17:12:50', NULL, NULL, NULL, NULL),
            (28, 'Site de Baguida', 9, '2023-04-06 17:13:09', '2023-04-06 17:13:09', NULL, NULL, NULL, NULL),
            (29, 'Master 1', 24, '2023-04-09 04:10:42', '2023-04-09 04:10:42', NULL, NULL, NULL, NULL),
            (30, 'BST Action Commerciale', 25, '2023-04-20 11:46:06', '2023-04-20 11:46:06', NULL, NULL, NULL, NULL),
            (31, 'Colpro Institut', 26, '2023-04-20 12:15:28', '2023-04-20 12:15:28', NULL, NULL, NULL, NULL),
            (32, 'Lomé 2', 27, '2023-04-20 12:15:48', '2023-04-20 12:15:48', NULL, NULL, NULL, NULL),
            (33, 'Réclamation #1', 28, '2023-05-10 15:47:00', '2023-05-10 15:47:00', NULL, 1, 1, NULL),
            (34, 'Question sur le service', 28, '2023-05-10 15:47:25', '2023-05-10 15:47:25', NULL, 1, 1, NULL),
            (35, 'Gestion', 3, '2023-05-19 12:41:25', '2023-05-19 12:41:25', NULL, 1, 1, NULL),
            (36, ' Stock', 3, '2023-05-19 12:41:25', '2023-05-19 12:41:25', NULL, 1, 1, NULL);
        ");
    }
}
