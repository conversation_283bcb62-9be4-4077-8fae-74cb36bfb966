<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Seeder;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $permissions = [
            // User Management
            [
                'name' => 'view_users',
                'display_name' => 'View Users',
                'description' => 'Can view users list and details',
                'module' => 'users'
            ],
            [
                'name' => 'create_users',
                'display_name' => 'Create Users',
                'description' => 'Can create new users',
                'module' => 'users'
            ],
            [
                'name' => 'edit_users',
                'display_name' => 'Edit Users',
                'description' => 'Can edit existing users',
                'module' => 'users'
            ],
            [
                'name' => 'delete_users',
                'display_name' => 'Delete Users',
                'description' => 'Can delete users',
                'module' => 'users'
            ],

            // Document Management
            [
                'name' => 'view_documents',
                'display_name' => 'View Documents',
                'description' => 'Can view documents list and details',
                'module' => 'documents'
            ],
            [
                'name' => 'create_documents',
                'display_name' => 'Create Documents',
                'description' => 'Can create new documents',
                'module' => 'documents'
            ],
            [
                'name' => 'edit_documents',
                'display_name' => 'Edit Documents',
                'description' => 'Can edit existing documents',
                'module' => 'documents'
            ],
            [
                'name' => 'delete_documents',
                'display_name' => 'Delete Documents',
                'description' => 'Can delete documents',
                'module' => 'documents'
            ],

            // Admin Panel
            [
                'name' => 'access_admin',
                'display_name' => 'Access Admin Panel',
                'description' => 'Can access admin panel',
                'module' => 'admin'
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name']],
                $permission
            );
        }
    }
}
