<?php

require_once 'vendor/autoload.php';

// Load Laravel application
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use Illuminate\Support\Facades\DB;

try {
    DB::statement('DROP TABLE IF EXISTS users');
    echo "Successfully dropped users table if it existed.\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
