/*-----------------------------------------
* Theme Name: Listing Hub
* Author: Themez Hub
* Version: 1.0
* Last Change: Dec 27 2017
  Author URI    : http://www.themezhub.com/
-------------------------------------------------------------------*/
/*------------------------------------------------------------------
# Font Links
# general setting
	General: Heading
	General: Form and Button
	General: Pagination
# Global Settings
	Global Settings: Custom Button
	Global Settings: Padding Style
	Global Settings: Margin Style
	Global Settings: Background Style
	Global Settings: Transparent Background Style
	Global Settings: Transparent Background with border Style
	Global Settings: Color Style
	Global Settings: Border Color Style
	Global Settings: Status BG Style
	Global Settings: Height
	Global Settings: Width Style
	Global Settings: Line Height
	Global Settings: Font Size
	Global Settings: Label Background
	Global Settings: Custom checkbox
	Global Setting: Multiple Bg Color For Category
	Global Border Styles
	Global Settings: Listing & Blockquote Style
# Navigation
	Default Navigation
	Transparent Navigation
	
# Home Banner Styles
	Common Banner Style
	Home 1 Banner
	Home 2 Banner
	Banner Search form style
# listing design
	Listing In Grid Style
	Liting Verticle Style

-----------------------------------------------*/


/* ==========================================================================
Font Links
========================================================================== */

@import url('https://fonts.googleapis.com/css?family=Montserrat:300,400,500,600,700');;
@import url('https://fonts.googleapis.com/css?family=Raleway:300,400,500');
@import url('https://fonts.googleapis.com/css?family=Crimson+Text:400,600');

/* ==========================================================================
general setting
========================================================================== */
/*------ General: Common Style ---------*/
html,body {
	width:100%;
	height: auto;
	margin:0;
	padding:0;
	overflow-x: hidden;
}

body {
	background:#ffffff;
	font-family: 'Montserrat', sans-serif;
	font-weight:400;
	position: relative;
	font-size:14px;
	color:#8995a2;
	line-height:24px;
}

p {
    position: relative;
    text-transform: capitalize;
    font-size: 13.5px;
    color:#677782;
    line-height:1.8;
    font-family: 'Montserrat', sans-serif;
    -webkit-transition: .2s ease-in;
    -moz-transition: .2s ease-in;
    transition: .2s ease-in;
}
h1, h2, h3, h4, h5, h6 {
  margin-bottom: .25em;
  color:#334e6f;
}
p a{
	/* color:#ff3a72; */
	color:#EA4F0C;
}
a{
	color:#334e6f;
}

a,
a:active,
a:focus,
a:hover{
	color:#334e6f;
	outline:none;
	text-decoration:none;
	-webkit-transition: all 0.3s ease-in-out;
	-moz-transition: all 0.3s ease-in-out;
	-o-transition: all 0.3s ease-in-out;
	-ms-transition: all 0.3s ease-in-out;
	transition: all 0.3s ease-in-out;
}
.badge{
	font-weight:600;
}
.text-center {
    text-align: center;
}

section{
	padding:4em 0 3em 0;
	background:#ffffff;
}
.small-pad{
	padding:1em 0 1.5em 0;
}
section.gray{
	background:#eff2f5;
}
.light-gray{
	background:#F5F7F8;
}
.light-bg{
	background:#ffffff;
}
a.btn.call-btn {
	background: #ffffff;
	border-radius: 4px;
	padding: 0.8em 2em;
	color:#ff3a72;
	text-transform: capitalize;
}
.d-block{
	display:block;
	margin-bottom:10px;
}
.card {
    position: relative;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-direction: column;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid #e8eef1;
    border-radius: .25rem;
	margin-bottom:30px;
}
.card-header {
	display: -webkit-box;
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
	padding: .85rem 1.25rem;
    margin-bottom: 0;
	border-bottom:1px solid #eaeff5;
}
.card-header > * {
    margin-left: 4px;
    margin-right: 4px;
}

.card-header>*:first-child {
    margin-left: 0px;
}

.card-header>*:last-child {
    margin-right: 0;
}
.card-footer {
	display: -webkit-box;
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
	padding: .85rem 1.25rem;
    margin-bottom: 0;
	border-top:1px solid #eaeff5;
}
.card-footer > * {
    margin-left: 4px;
    margin-right: 4px;
}

.card-footer>*:first-child {
    margin-left: 0px;
}

.card-footer>*:last-child {
    margin-right: 0;
}
.card-body {
    -ms-flex: 1 1 auto;
    flex: 1 1 auto;
    padding: 1.25rem;
}
label.btn-bs-file.btn {
    width: 100%;
	background:#fbfdff;
    border: 1px solid #e8eef1;
}
.btn-bs-file input[type="file"] {
    display: inline-block;
}
/*----------- General: Heading ------------*/
.heading{
	padding:0px 0 25px 0;
	margin-bottom:20px;
	text-align:center;
}
.heading h2{
	font-weight:500;
	margin-top:0;
	text-transform:capitalize;
	font-family: 'Montserrat', sans-serif;
}
.heading h2 span{
	color:#ff3a72;
}
.heading p{
	line-height:1.7;
	font-size:17px;
}

/*--------- General: Form and Button ------------*/
button:hover, input:hover, input:focus, button:focus{
	outline:none;
}
.btn {
    border-radius: 2px;
    -webkit-box-shadow: none;
    box-shadow: none;
    font-weight: 400;
    position: relative;
    border: 1px solid;
	background-image: none;
	padding: 10px 15px;
	transition:all ease 0.4s;
}
.btn-square {
    width: 44px;
    height: 42px;
    display: inline-block;
    text-align: center;
    line-height: 42px;
    font-size: 16px;
    border-radius: 2px;
    margin: 5px;
	transition:all ease 0.4s;
}
.btn-square-large{
	width:55px;
    height:55px;
    display: inline-block;
    text-align: center;
    line-height:55px;
    font-size:18px;
    border-radius: 2px;
    margin:7px;
}
.light-gray-btn{
	background:#e8edf1;
	border:1px solid #e5eaef;
}
.light-gray-btn:hover, .light-gray-btn:focus{
	color:#ffffff;
	background:#78909C;
	border:1px solid #78909C;
}
.btn-general-white-bg{
	background:#ffffff;
	color:#ff3a72;
	border-color:#ffffff;
}
.btn-general-white-bg:hover, .btn-general-white-bg:focus{
	background: #EA4F0C;
	color:#ffffff;
	border-color: #EA4F0C;
}
.btn-general-theme-bg{
	background: #EA4F0C;
	color:#ffffff;
	border-color: #EA4F0C;
}
.btn-general-theme-bg:hover, .btn-general-theme-bg:focus{
	background:#ffffff;
	color:#ff3a72;
	border-color:#ffffff;
}
.btn-general-theme-trans-bg{
	background:rgba(255, 58, 114,0.1);
	border-color: #EA4F0C;
	color:#ff3a72;
}
.btn-general-theme-trans-bg:hover, .btn-general-theme-trans-bg:focus{
	background:rgba(255, 58, 114,1);
	border-color: #EA4F0C;
	color:#ffffff;
}
.full-width{
	width:100%;
}
.btn-width-200{
	width:200px;
	margin-left: auto;
	margin-right: auto;
}
.btn-radius{
	border-radius:50px;
}
.form-control {
    height:50px;
    border: 1px solid #dde6ef;
	margin-bottom:10px;
    box-shadow: none;
    border-radius: 0;
    background: #fbfdff;
    font-size: 15px;
	color:#445461;
    font-weight: 400;
}

.bootstrap-select.form-control {
    margin-bottom: 0;
    padding: 0;
	margin-bottom:10px;
    border: 1px solid #dde6ef;
}
.form-control:hover, .form-control:focus{
	border: 1px solid #EA4F0C;
	-webkit-box-shadow:0 1px 1px rgba(7,177,7,.075);
	box-shadow:0 1px 1px rgba(7,177,7,.075);
	outline:none;
}
.form-control .btn.dropdown-toggle.btn-default:hover, .form-control .btn.dropdown-toggle.btn-default:focus{
	border:none;
	-webkit-box-shadow:none;
	box-shadow:none;
	outline:none;
}
span.input-group-addon {
    color: #8995a2;
    border-color:#dde6ef;
    background: #fbfdff;
    border-left: 0;
}
nav.navbar.navbar-default.navbar-fixed.white.bootsnav.shadow.on.menu-center.no-full{
	box-shadow:0px 5px 20px rgba(0, 0, 0, 0.05);
	-webkit-box-shadow:0px 5px 20px rgba(0, 0, 0, 0.05);
	-moz-box-shadow:0px 5px 20px rgba(0, 0, 0, 0.05);
}
.bootstrap-select button.btn.dropdown-toggle.bs-placeholder.btn-default {
    background: transparent;
    height: 46px;
    border: 1px solid transparent;
    color:#445461;
    text-shadow: none;
    border-radius: 0px;
    box-shadow: none;
}
.btn.btn-primary {
    border: 1px solid #EA4F0C;
    border-radius: 0px;
    width: 100%;
    height: 46px;
    background: #EA4F0C;
    text-transform: capitalize;
    font-size: 16px;
}
.btn.btn-primary:hover, .btn.btn-primary:focus{
    border: 1px solid #EA4F0C;
    border-radius: 0px;
    width: 100%;
    height: 46px;
    background: #EA4F0C;
    text-transform: capitalize;
    font-size: 16px;
}
.btn-default.active.focus, .btn-default.active:focus, .btn-default.active:hover, .btn-default:active.focus, .btn-default:active:focus, .btn-default:active:hover, .open>.dropdown-toggle.btn-default.focus, .open>.dropdown-toggle.btn-default:focus, .open>.dropdown-toggle.btn-default:hover {
    color: #333;
    background-color: transparent;
    border-color: transparent;
    height: 46px;
}
.bootstrap-select .dropdown-toggle:focus {
    outline:none !important;
    outline:none !important;
    outline-offset:0 !important;
}
.bootstrap-select.btn-group .dropdown-menu li a {
    padding: 8px 10px;
}
.bootstrap-select.btn-group .dropdown-menu li a:hover {
    box-shadow: none;
    background: #EA4F0C;
    color: #ffffff;
}
.btn-group.open .dropdown-toggle {
    -webkit-box-shadow: none;
    box-shadow: none;
}
.btn-default.active, .btn-default:active, .open>.dropdown-toggle.btn-default {
    color:#445461;
    background-color: transparent;
    border-color: transparent;
}
button.btn.dropdown-toggle.btn-default {
    background: transparent;
    border: none;
    box-shadow: none;
    height: 46px;
}
.dropdown-menu>.active>a, .dropdown-menu>.active>a:hover, .dropdown-menu>.active>a:focus {
    background-color:#EA4F0C;
    /* background-color:#EA4F0C; */
}
/*------ Choosen Select Box ----*/
.chosen-container-single .chosen-single {
	background:#fbfdff;
    border: 1px solid #dde6ef;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    color: #445461;
    height: 50px;
	line-height: 50px;
	margin-bottom:10px;    
}
.chosen-container-single .chosen-single div {
    top: 8px;
}
.chosen-container-active.chosen-with-drop .chosen-single {
    background-color: #fff;
    border: 1px solid #dde6ef;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    -webkit-transition: border linear 0.2s, box-shadow linear 0.2s;
    -o-transition: border linear 0.2s, box-shadow linear 0.2s;
    transition: border linear 0.2s, box-shadow linear 0.2s;
}
.chosen-container-single .chosen-search input[type="text"] {
    border: 1px solid #dde6ef;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    margin: 1px 0;
    padding: 4px 20px 4px 4px;
    width: 100%;
}
.chosen-container .chosen-results li.highlighted {
    background-color: #f4f5f7;
    background-image: none;
    color: #445661;
}
.chosen-container-active.chosen-with-drop .chosen-single div b {
    background-position: -15px 7px;
}
.chosen-container .chosen-drop {
    background: #fff;
    border: 1px solid #dde6ef;
    border-bottom-right-radius: 4px;
    border-bottom-left-radius: 4px;
    -webkit-box-shadow: 0 2px 10px 0 #d8dde6;
    box-shadow: 0 2px 10px 0 #d8dde6;
    margin-top: -1px;
    position: absolute;
    top: 100%;
    left: -9000px;
    z-index: 1060;
}
/*---------- General: Pagination -------------*/
.pagination {
	display: table;
	padding-left: 0;
	margin: 20px 0;
	border-radius:4px;
	margin: 20px auto;
}
.pagination>li>a, .pagination>li>span {
    position: relative;
    float: left;
    padding: 0;
    margin: 5px;
    line-height: 1.42857143;
    color: #5a6f7c;
    text-decoration: none;
    background-color: #fff;
    border-radius: 50px;
    width: 37px;
    height: 37px;
    text-align: center;
    line-height: 37px;
	border: 1px solid #eaeff5;
	-webkit-box-shadow: 0 2px 10px 0 #d8dde6;
    box-shadow: 0 2px 10px 0 #d8dde6;
}
.pagination>.active>a, .pagination>.active>span, .pagination>.active>a:hover, .pagination>.active>span:hover, .pagination>.active>a:focus, .pagination>.active>span:focus, .pagination>li>a:hover, .pagination>li>a:focus {
	z-index: 2;
	color:#ff3a72;
	cursor:pointer;
	background-color:rgba(64,65,67,0.1);
	border-color: #EA4F0C;
}
.pagination li:first-child a {
	background: #EA4F0C;
	border: 1px solid #EA4F0C;
	border-radius:50px;
	color:#ffffff;
}
.pagination li:last-child a {
	background: #35434e;
	border: 1px solid #35434e;
	border-radius:50px;
	color:#ffffff;
}

/* ==========================================================================
    Global Settings
========================================================================== */
.theme-bg{
	background: #EA4F0C;
	color:#ffffff;
}
.theme-bg p{
	color:#ffffff;
}

.dark-bg{
	background:#2a3646;
}

.light-bg{
	background:#ffffff;
}

.gray-bg{
	background:#f4f5f7;
}

.theme-cl{
	color:#ff3a72;
}

.theme-overlap{
	background:url(../img/slider-2.jpg);
	background-position:center !important;
	background-size:cover !important;
	position:relative;
}
.theme-overlap:before{
	background: #EA4F0C;
}
.theme-overlap:before{
	opacity:0.8;
	content:"";
	display:block;
	left:0;
	right:0;
	top:0;
	bottom:0;
	height:100%;
	width:100%;
	position:absolute;
}
/*---------- Global Settings: Custom Button -----------*/
.btn-radius{
	border-radius:50px;
}
.theme-btn {
    background: #EA4F0C;
    border: 1px solid #EA4F0C;
	color:#ffffff;
	text-transform:uppercase;
}
.theme-btn:hover, .theme-btn:focus{
	color:#ffffff;
	background: #EA4F0C;
    border: 1px solid #EA4F0C;
}
.btn.theme-btn-outlined, a.theme-btn-outlined{
	background:transparent;
	border:1px solid #ff3a72;
	color:#ff3a72;	
}

/*  */
.btn.dark-btn-outlined, a.theme-btn-outlined{
	background:transparent;
	border:1px solid #24324a;
	color:#24324a;	
}
.btn.dark-btn-outlined:hover, a.dark-btn-outlined:hover, .btn.dark-btn-outlined:focus, a.dark-btn-outlined:focus{
	background: #24324a;
	border-color: #24324a;
	color:#ffffff;
}
/*  */
.btn.theme-btn-outlined:hover, a.theme-btn-outlined:hover, .btn.theme-btn-outlined:focus, a.theme-btn-outlined:focus{
	background: #EA4F0C;
	border-color: #EA4F0C;
	color:#ffffff;
}
.btn.theme-btn-trans-radius, a.theme-btn-trans-radius{
	background: rgba(255, 58, 114,0.1);
    color: #EA4F0C;
    border-radius: 50px;
    border: 1px solid #EA4F0C;
}
.btn.theme-btn-trans-radius:hover, a.theme-btn-trans-radius:hover, .btn.theme-btn-trans-radius:focus, a.theme-btn-trans-radius:focus{
	background: rgba(255, 58, 114,1);
    color: #ffffff;
    border-radius: 50px;
    border: 1px solid #EA4F0C;
}
.btn.theme-btn-trans, a.theme-btn-trans{
	background: rgba(255, 58, 114,0.1);
    color: #EA4F0C;
    border-radius:2px;
    border: 1px solid #EA4F0C;
}
.btn.theme-btn-trans:hover, a.theme-btn-trans:hover, .btn.theme-btn-trans:focus, a.theme-btn-trans:focus{
	background: rgba(255, 58, 114,1);
    color: #ffffff;
    border-radius:2px;
    border: 1px solid #EA4F0C;
}
.btn.btn-light-outlined, a.btn-light-outlined{
	background:rgba(255,255,255,0.1);
	border:1px solid #ffffff;
	color:#ffffff;
}
.btn.btn-light-outlined:hover, a.btn-light-outlined:hover, .btn.btn-light-outlined:focus, a.btn-light-outlined:focus{
	background:rgba(255,255,255,1);
	border:1px solid #ffffff;
	color:#ff3a72;
}
.btn.light-btn, a.light-btn{
	background:#ffffff;
	border:1px solid #ffffff;
	color:#ff3a72;
}
.btn.light-btn:hover, .btn.light-btn:focus, a.light-btn:hover, a.light-btn:focus{
	background: #EA4F0C;
	border:1px solid #ff3a72;
	color:#ffffff;
}
/*------- Global Settings: Padding Style ----------*/
html body .padd-0{
	padding:0px;
}
html body .padd-5{
	padding:5px;
}
html body .padd-10{
	padding:10px;
}
html body .padd-15{
	padding:15px;
}
html body .padd-20{
	padding:20px;
}
html body .padd-l-0{
	padding-left:0px;
}
html body .padd-l-5{
	padding-left:5px;
}
html body .padd-l-10{
	padding-left:10px;
}
html body .padd-l-15{
	padding-left:15px;
}
html body .padd-r-0{
	padding-right:0px;
}
html body .padd-r-5{
	padding-right:5px;
}
html body .padd-r-10{
	padding-right:10px;
}
html body .padd-r-10{
	padding-right:15px;
}
html body .padd-top-0{
	padding-top:0px;
}
html body .padd-top-5{
	padding-top:5px;
}
html body .padd-top-10{
	padding-top:10px;
}
html body .padd-top-15{
	padding-top:15px;
}
html body .padd-top-20{
	padding-top:20px;
}
html body .padd-top-25{
	padding-top:25px;
}
html body .padd-top-30{
	padding-top:30px;
}
html body .padd-top-40{
	padding-top:40px;
}
html body .padd-bot-0{
	padding-bottom:0px;
}
html body .padd-bot-5{
	padding-bottom:5px;
}
html body .padd-bot-10{
	padding-bottom:10px;
}
html body .padd-bot-15{
	padding-bottom:15px;
}
html body .padd-bot-20{
	padding-bottom:20px;
}
html body .padd-bot-25{
	padding-bottom:25px;
}
html body .padd-bot-30{
	padding-bottom:30px;
}
html body .padd-bot-40{
	padding-bottom:40px;
}

/*------- Global Settings: Margin Style ----------*/
html body .mrg-0{
	margin:0px;
}
html body .mrg-5{
	margin:5px;
}
html body .mrg-10{
	margin:10px;
}
html body .mrg-15{
	margin:15px;
}
html body .mrg-20{
	margin:20px;
}
html body .mrg-l-0{
	margin-left:0px;
}
html body .mrg-l-5{
	margin-left:5px;
}
html body .mrg-l-10{
	margin-left:10px;
}
html body .mrg-l-15{
	margin-left:15px;
}
html body .mrg-r-0{
	margin-right:0px;
}
html body .mrg-r-5{
	margin-right:5px;
}
html body .mrg-r-10{
	margin-right:10px;
}
html body .mrg-r-15{
	margin-right:15px;
}
html body .mrg-top-0{
	margin-top:0px;
}
html body .mrg-top-5{
	margin-top:5px;
}
html body .mrg-top-10{
	margin-top:10px;
}
html body .mrg-top-15{
	margin-top:15px;
}
html body .mrg-top-20{
	margin-top:20px;
}
html body .mrg-top-25{
	margin-top:25px;
}
html body .mrg-top-30{
	margin-top:30px;
}
html body .mrg-top-40{
	margin-top:40px;
}
html body .mrg-bot-0{
	margin-bottom:0px;
}
html body .mrg-bot-5{
	margin-bottom:5px;
}
html body .mrg-bot-10{
	margin-bottom:10px;
}
html body .mrg-bot-15{
	margin-bottom:15px;
}
html body .mrg-bot-20{
	margin-bottom:20px;
}
html body .mrg-bot-25{
	margin-bottom:25px;
}
html body .mrg-bot-30{
	margin-bottom:30px;
}
html body .mrg-bot-40{
	margin-bottom:40px;
}
html body .extra-mrg-5{
	margin:0 -5px;
}
html body .extra-mrg-10{
	margin:0 -10px;
}
html body .extra-mrg-15{
	margin:0 -15px;
}
html body .extra-mrg-20{
	margin:0 -20px;
}
/*------- Global Settings: Background Style ----------*/
html body .bg-info{
	background:#01b2ac;
}
html body .bg-primary{
	background:#1194f7;
}
html body .bg-danger{
	background:#f21136;
}
html body .bg-warning{
	background:#ff9800;
}
html body .bg-success{
	background:#0fb76b;
}
html body .bg-purple{
	background:#c580ff;
}
html body .bg-default{
	background:#283447;
}
/*------- Global Settings: Transparent Background Style ----------*/
html body .bg-trans-info{
	background:rgba(2, 182, 179,0.12);
}
html body .bg-trans-primary{
	background:rgba(17, 148, 247,0.12);
}
html body .bg-trans-danger{
	background:rgba(242, 17, 54,0.12);
}
html body .bg-trans-warning{
	background:rgba(255, 152, 0,0.12);
}
html body .bg-trans-success{
	background:rgba(15, 183, 107,0.12);
}
html body .bg-trans-purple{
	background:rgba(197, 128, 255,0.12);
}
html body .bg-trans-default{
	background:rgba(40, 52, 71,0.12);
}
/*------- Global Settings: Transparent Background with border Style ----------*/
html body .bg-info-br{
	border:1px solid #01b2ac;
	background:rgba(2, 182, 179,0.12);
}
html body .bg-primary-br{
	border:1px solid #1194f7;
	background:rgba(17, 148, 247,0.12);
}
html body .bg-danger-br{
	border:1px solid #f21136;
	background:rgba(242, 17, 54,0.12);
}
html body .bg-warning-br{
	border:1px solid #ff9800;
	background:rgba(255, 152, 0,0.12);
}
html body .bg-success-br{
	border:1px solid #0fb76b;
	background:rgba(15, 183, 107,0.12);
}
html body .bg-purple-br{
	border:1px solid #c580ff;
	background:rgba(197, 128, 255,0.12);
}
html body .bg-default-br{
	border:1px solid #283447;
	background:rgba(40, 52, 71,0.12);
}
/*------- Global Settings: Color Style ----------*/
html body .cl-info{
	color:#01b2ac;
}
html body .cl-primary{
	color:#1194f7;
}
html body .cl-danger{
	color:#f21136;
}
html body .cl-warning{
	color:#ff9800;
}
html body .cl-success{
	color:#0fb76b;
}
html body .cl-purple{
	color:#c580ff;
}
html body .cl-default{
	color:#283447;
}
html body .cl-#ffffff{
	color:#ffffff;
}
/*------- Global Settings: Border Color Style ----------*/
html body .br-info{
	border-color:#01b2ac;
}
html body .br-primary{
	border-color:#1194f7;
}
html body .br-danger{
	border-color:#f21136;
}
html body .br-warning{
	border-color:#ff9800;
}
html body .br-success{
	border-color:#0fb76b;
}
html body .br-purple{
	border-color:#c580ff;
}
html body .br-default{
	border-color:#283447;
}
/*------------ Global Settings: Status BG Style --------------*/
html body .bg-online{
	background:#68c70b;
}
html body .bg-offline{
	background:#e02b0d;
}
html body .bg-busy{
	background:#2196f3;
}
html body .bg-working{
	background:#ff9800;
}
/*---------- Global Settings: Height ----------*/
html body .normal-height{
	height:46px;
}
html body .height-10{
	height:10px;
}	
html body .height-20{
	height:20px;
}
html body .height-30{
	height:30px;
}
html body .height-40{
	height:40px;
}
html body .height-50{
	height:50px;
}
html body .height-60{
	height:60px;
}
html body .height-70{
	height:70px;
}
html body .height-80{
	height:80px;
}
html body .height-90{
	height:90px;
}
html body .height-100{
	height:100px;
}
html body .height-110{
	height:110px;
}
html body .height-120{
	height:120px;
}
html body .height-130{
	height:130px;
}
html body .height-140{
	height:140px;
}
html body .height-150{
	height:150px;
}
html body .height-160{
	height:160px;
}
html body .height-170{
	height:170px;
}
html body .height-180{
	height:180px;
}
html body .height-190{
	height:190px;
}
html body .height-200{
	height:200px;
}
html body .height-210{
	height:210px;
}
html body .height-220{
	height:220px;
}
html body .height-230{
	height:230px;
}
html body .height-240{
	height:240px;
}
html body .height-250{
	height:250px;
}
html body .height-260{
	height:260px;
}
html body .height-270{
	height:270px;
}
html body .height-280{
	height:280px;
}
html body .height-290{
	height:290px;
}
html body .height-300{
	height:300px;
}
html body .height-350{
	height:350px;
}
html body .height-400{
	height:400px;
}
html body .height-450{
	height:450px;
}
/*----------- Global Settings: Width Style -----------*/
html body .full-width{
	width:100%;
}
html body .width-30{
	width:30px;
}
html body .width-40{
	width:40px;
}
html body .width-50{
	width:50px;
}
html body .width-60{
	width:60px;
}
html body .width-70{
	width:70px;
}
html body .width-80{
	width:80px;
}
html body .width-90{
	width:90px;
}
html body .width-100{
	width:100px;
}
html body .width-110{
	width:110px;
}
html body .width-120{
	width:20px;
}
html body .width-130{
	width:130px;
}
html body .width-140{
	width:140px;
}
html body .width-150{
	width:150px;
}
html body .width-160{
	width:160px;
}
html body .width-170{
	width:170px;
}
html body .width-180{
	width:180px;
}
html body .width-190{
	width:190px;
}
html body .width-200{
	width:200px;
}
html body .width-210{
	width:210px;
}
html body .width-220{
	width:220px;
}
html body .width-230{
	width:230px;
}
html body .width-240{
	width:240px;
}
html body .width-250{
	width:250px;
}
html body .width-260{
	width:260px;
}
html body .width-270{
	width:270px;
}
html body .width-280{
	width:280px;
}
html body .width-290{
	width:290px;
}
html body .width-300{
	width:300px;
}
/*---------- Global Settings: Line Height ---------*/
html body .line-height-10{
	line-height:10px;
}	
html body .line-height-12{
	line-height:12px;
}
html body .line-height-14{
	line-height:14px;
}
html body .line-height-16{
	line-height:16px;
}
html body .line-height-18{
	line-height:18px;
}
html body .line-height-20{
	line-height:20px;
}
html body .line-height-22{
	line-height:22px;
}
html body .line-height-24{
	line-height:24px;
}
html body .line-height-26{
	line-height:26px;
}
html body .line-height-28{
	line-height:28px;
}
html body .line-height-30{
	line-height:30px;
}
html body .line-height-32{
	line-height:32px;
}
html body .line-height-34{
	line-height:34px;
}
html body .line-height-36{
	line-height:36px;
}
html body .line-height-38{
	line-height:38px;
}
html body .line-height-40{
	line-height:40px;
}
html body .line-height-42{
	line-height:42px;
}
html body .line-height-44{
	line-height:44px;
}
html body .line-height-46{
	line-height:46px;
}
html body .line-height-48{
	line-height:48px;
}
html body .line-height-50{
	line-height:50px;
}
html body .line-height-60{
	line-height:60px;
}
html body .line-height-70{
	line-height:70px;
}
html body .line-height-80{
	line-height:80px;
}
html body .line-height-90{
	line-height:90px;
}
html body .line-height-100{
	line-height:100px;
}
html body .line-height-110{
	line-height:110px;
}
html body .line-height-120{
	line-height:120px;
}
html body .line-height-130{
	line-height:130px;
}
html body .line-height-140{
	line-height:140px;
}
html body .line-height-150{
	line-height:150px;
}
html body .line-height-160{
	line-height:160px;
}
html body .line-height-170{
	line-height:170px;
}
html body .line-height-180{
	line-height:180px;
}
html body .line-height-190{
	line-height:190px;
}
html body .line-height-200{
	line-height:200px;
}
html body .line-height-210{
	line-height:210px;
}
html body .line-height-220{
	line-height:220px;
}
html body .line-height-230{
	line-height:230px;
}
html body .line-height-240{
	line-height:240px;
}
html body .line-height-250{
	line-height:250px;
}
html body .line-height-260{
	line-height:260px;
}
html body .line-height-270{
	line-height:270px;
}
html body .line-height-280{
	line-height:280px;
}
html body .line-height-290{
	line-height:290px;
}
html body .line-height-300{
	line-height:300px;
}
html body .line-height-350{
	line-height:350px;
}
html body .line-height-400{
	line-height:400px;
}
html body .line-height-450{
	line-height:450px;
}
/*---------- Global Settings: Font Size ----------*/
html body .font-10{
	font-size:10px;
}
html body .font-12{
	font-size:12px;
}
html body .font-13{
	font-size:13px;
}
html body .font-16{
	font-size:16px;
}
html body .font-18{
	font-size:18px;
}
html body .font-15{
	font-size:15px;
}
html body .font-20{
	font-size:20px;
}
html body .font-25{
	font-size:25px;
}
html body .font-30{
	font-size:30px;
}
html body .font-35{
	font-size:35px;
}
html body .font-40{
	font-size:40px;
}
html body .font-45{
	font-size:45px;
}
html body .font-50{
	font-size:50px;
}
html body .font-60{
	font-size:60px;
}
html body .font-70{
	font-size:70px;
}
html body .font-80{
	font-size:80px;
}
html body .font-90{
	font-size:90px;
}
html body .font-100{
	font-size:100px;
}
html body .font-bold{
	font-weight:bold;
}
html body .font-normal{
	font-weight:400;
}
html body .font-midium{
	font-weight:500;
}
html body .font-light{
	font-weight:300;
}
html body .font-italic{
	font-style:italic;
}
/*---------- Global Settings: Label Background ----------*/
html body .label-info{
	background:#01b2ac;
}
html body .label-primary{
	background:#1194f7;
}
html body .label-danger{
	background:#f21136;
}
html body .label-warning{
	background:#ff9800;
}
html body .label-success{
	background:#0fb76b;
}
html body .label-purple{
	background:#c580ff;
}
html body .label-default{
	background:#283447;
}
/*----------- Global Settings: Custom checkbox -----------*/
.custom-checkbox {
	position: relative;
}
.custom-checkbox input[type="checkbox"] {    
	opacity: 0;
	position: absolute;
	margin: 5px 0 0 3px;
	z-index: 9;
}
.custom-checkbox label:before{
	width: 18px;
	height: 18px;
}
.custom-checkbox label:before {
	content: '';
	margin-right: 10px;
	display: inline-block;
	vertical-align: text-top;
	background: #ffffff;
	border: 1px solid #bbb;
	border-radius: 2px;
	box-sizing: border-box;
	z-index: 2;
}
.custom-checkbox input[type="checkbox"]:checked + label:after {
	content: '';
	position: absolute;
	left: 6px;
	top:2px;
	width: 6px;
	height: 11px;
	border: solid #000;
	border-width: 0 3px 3px 0;
	transform: inherit;
	z-index: 3;
	transform: rotateZ(45deg);
}
.custom-checkbox input[type="checkbox"]:checked + label:before {
	border-color: #EA4F0C;
	background: #EA4F0C;
}
.custom-checkbox input[type="checkbox"]:checked + label:after {
	border-color: #fff;
}
.custom-checkbox input[type="checkbox"]:disabled + label:before {
	color: #b8b8b8;
	cursor: auto;
	box-shadow: none;
	background: #ddd;
}

/*------------ Global Setting: Multiple Bg Color For Category ---------*/
html body .bg-a{
	background:#f73d51;
}
html body .bg-b{
	background:#8a7cd9;
}
html body .bg-c{
	background:#ffb390;
}
html body .bg-d{
	background:#37b475;
}
html body .bg-e{
	background:#4b5e6c;
}
html body .bg-f{
	background:#f5b83b;
}
html body .bg-g{
	background:#5565d0;
}
html body .bg-h{
	background: #18bad9;
}
html body .bg-i{
	background:#433c63;
}
html body .bg-j{
	background:#ad4f87;
}
html body .bg-k{
	background:#ee7d4e;
}
html body .bg-l{
	background:#ff465a;
}
html body .bg-m{
	background:#f5b83b;
}
html body .bg-o{
	background:#18bad9;
}
html body .bg-p{
	background:#6877de;
}
html body .bg-q{
	background: #14af69;
}
html body .bg-r{
	background:#576977;
    color: #576977;
}
html body .bg-s{
	background:#fd5c05;
}
html body .bg-t{
	background:#8a7cd9;
}
html body .bg-u{
	background:#ff465a;
}
html body .bg-v{
	background: #8a7cd9;
}
html body .bg-x{
	background:#18bad9;
}
html body .bg-y{
	background: #f5b83b;
}
html body .bg-z{
	background:#ff8645;
}
/*-------- Global Border Styles --------------*/
html body .border-0{
	border:none;
}
html body .border-left{
	border-left:1px solid #dde6ef;
}
html body .border-right{
	border-right:1px solid #dde6ef;
}
html body .border-top{
	border-top:1px solid #dde6ef;
}
html body .border-bottom{
	border-bottom:1px solid #dde6ef;
}
html body .border-around{
	border:1px solid #dde6ef;
}

/*---------- Global Settings: Listing & Blockquote Style ---------------*/
ol, ul {
    margin-top: 0;
    margin-bottom: 10px;
    padding: 0;
}

ol.check-listing > li, ul.check-listing > li {
    position: relative;
    letter-spacing: -0.2px;
	list-style:none;
}
ol.check-listing > li:before, ul.check-listing > li:before {
    content: "\f00c";
    font-family: 'FontAwesome';
    font-size: 1em;
	margin-right: 7px;
    color: #EA4F0C;
}

.quote-card {
  background: #fff;
  padding: 20px;
  padding-left: 50px;
  box-sizing: border-box;
  position: relative;
  min-height: 100px;
  border-left:none;
}
.quote-card p {
  font-size: 22px;
  line-height: 1.5;
  margin: 0;
  max-width:90%;
}
.quote-card cite {
  font-size: 16px;
  margin-top: 10px;
  display: block;
  font-weight:400;
  color:#ff3a72;
}
.quote-card:before {
  font-family: Georgia, serif;
  content: "“";
  position: absolute;
  top: 10px;
  left: 10px;
  font-size:50px;
  color:#ff3a72;
  font-weight: normal;
}


/*==========================================
listing design
==========================================*/
/*----------- Listing In Grid Style ------------*/
.listing-shot {
	border:1px solid #eaeff5;
	box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);
	-webkit-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);
	-moz-box-shadow: 0px 0px 10px 1px rgba(71, 85, 95,0.08);
	overflow: hidden;
	margin-bottom:30px;
	border-radius:6px;
	background: #ffffff;
	padding:0;
	display: inline-block;
	width: 100%;
	position:relative;
	transition:all ease 0.4s;
}
.listing-shot:hover, .listing-shot:focus{
	-webkit-box-shadow: 0 10px 30px 0 rgba(58,87,135, 0.15);
    -moz-box-shadow: 0 10px 30px 0 rgba(58,87,135, 0.15);
    box-shadow: 0 10px 30px 0 rgba(58,87,135, 0.15);
}
.listing-shot-img {
    position: relative;
}
.grid-style .listing-shot-img {
    max-height: 200px;
    height: 200px;
    overflow: hidden;
}
span.like-listing {
    position: absolute;
    right:20px;
    bottom:20px;
}
span.like-listing i {
	background: #EA4F0C;
	border: 1px solid transparent;
	text-align:center;
	line-height:42px;
	width:42px;
	height:42px;
	border-radius: 50%;
	color: #ffffff;
	font-size:17px;
}
.listing-shot-info.rating {
    border-top:1px solid rgba(71, 85, 95,0.11);
}
.listing-shot-caption, .listing-shot-info {
    padding: 10px 15px;
	position:relative;
}
.listing-shot-caption h4 {
    margin-bottom: 3px;
	font-size:17px;
}
p.listing-location {
    margin-bottom: 0;
}
.listing-detail-info span {
    display: block;
	color: #667582;
    margin-bottom: 7px;
}
.listing-detail-info span i {
    margin-right: 7px;
	color: #798a98;
    font-size: 15px;
}
.listing-shot-info.rating i {
    color: #a1acb5;
}
.listing-shot-info.rating i.color {
    color: #FF9800;
}
.listing-shot-info.rating a.detail-link {
    color: #EA4F0C;
    text-transform: uppercase;
    font-size: 14px;
    float: right;
    font-weight: 500;
}

.listing-badge.now-open {
    background-color: #54ba1d;
}

.listing-badge {
    background-color: #333;
    float: left;
    position: absolute;
    transform: rotate(-45deg);
    left: -60px;
    top: 30px;
    text-align: center;
    width: 200px;
    font-size: 12.5px;
    margin: 0;
    z-index: 999;
    color: #fff;
    font-weight: 500;
    line-height: 28px;
}
span.like-listing.style-2 {
    top: -20px;
}
span.listing-price {
    background: rgba(0,0,0,0.17);
    color: #ffffff;
    position: absolute;
    bottom: 15px;
    left: 15px;
    padding: 1px 18px;
    letter-spacing: 1px;
    border-radius: 2px;
}
.featured-listing{
    position: absolute;
    left: 0;
    top:40px;
	z-index: 1;
}
.featured-listing:before {
    content: "";
    border-top:43px solid #ff8f00;
    border-right:43px solid transparent;
}
.featured-listing:after{
	content: "\f005";
	display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
	position:absolute;
	left: 6px;
    top: -33px;
    color:#ffdd7f;
}
span.approve-listing {
    width: 25px;
    height: 25px;
    background: #10aa08;
    position: absolute;
    top: 15px;
    right: 15px;
    border-radius: 50%;
    color: #ffffff;
    text-align: center;
    font-size: 10px;
    border: 2px solid #9fd402;
    line-height: 21px;
}

