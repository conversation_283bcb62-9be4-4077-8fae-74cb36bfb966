/*-----General Information----*/
nav.navbar.bootsnav ul.nav li.active>a, .navbar-default .navbar-nav>li>a:focus, .navbar-default .navbar-nav>li>a:hover {
    color: #E32636 !important;
}
.heading h2 span {
    color:#E32636;
}
.theme-cl {
    color:#E32636;
}
.theme-bg {
    background:#E32636;
}
span.like-listing i {
    background:#E32636;
    border: 1px solid transparent;
}
.theme-overlap:before {
    background:#E32636;
}
.feature-box span {
    background:#E32636;
}
.btn-default.active.focus, .btn-default.active:focus, .btn-default.active:hover, .btn-default:active.focus, .btn-default:active:focus, .btn-default:active:hover, .open>.dropdown-toggle.btn-default.focus, .open>.dropdown-toggle.btn-default:focus, .open>.dropdown-toggle.btn-default:hover {
    color: #fff;
    background-color:#E32636;
    border-color:#E32636;
}
/*---Category Colors-----*/
.category-boxs:hover .category-box-btn, .category-boxs:focus .category-box-btn {
    background:#E32636;
    border-color:#E32636;
}
.category-full-widget:hover .btn-btn-wrowse, .category-full-widget:focus .btn-btn-wrowse {
    background:#E32636;
    color: #ffffff;
}
.category-box-full.style-2:hover .category-box-2 i, .category-box-full.style-2:focus .category-box-2 i{
	background:#E32636;
	border-color:#E32636;
}
.cat-box-name .btn-btn-wrowse:hover, .cat-box-name .btn-btn-wrowse:focus {
    background:#E32636;
}
span.category-tag {
    color:#E32636;
    border: 1px solid #E32636;
}
/*---prices---*/
.active .package-header {
    background:#E32636;
}
button.btn.btn-package {
    background:#E32636;
}
/*----button colors---*/
.theme-btn {
    background:#E32636;
    border: 1px solid #E32636;
	color:#ffffff;
	text-transform:uppercase;
}
.theme-btn:hover, .theme-btn:focus{
	color:#ffffff;
	background:#E32636;
    border: 1px solid #E32636;
}
btn.theme-btn-outlined, a.theme-btn-outlined{
	background:transparent;
	border:1px solid #E32636;
	color:#ffffff;	
}
btn.theme-btn-outlined:hover, a.theme-btn-outlined:hover, btn.theme-btn-outlined:focus, a.theme-btn-outlined:focus{
	background:#E32636;
	border-color:#E32636;
	color:#ffffff;
}
btn.theme-btn-trans, a.theme-btn-trans{
	background: rgba(227, 38, 54,0.1);
    color:#E32636;
    border-radius: 50px;
    border: 1px solid #E32636;
}
btn.theme-btn-trans:hover, a.theme-btn-trans:hover, btn.theme-btn-trans:focus, a.theme-btn-trans:focus{
	background: rgba(227, 38, 54,1);
    color: #ffffff;
    border-radius: 50px;
    border: 1px solid #E32636;
}
btn.btn-light-outlined, a.btn-light-outlined{
	background:rgba(255,255,255,0.1);
	border:1px solid #ffffff;
	color:#ffffff;
}
btn.btn-light-outlined:hover, a.btn-light-outlined:hover, btn.btn-light-outlined:focus, a.btn-light-outlined:focus{
	background:rgba(255,255,255,1);
	border:1px solid #ffffff;
	color:#E32636;
}
btn.light-btn, a.light-btn{
	background:#ffffff;
	border:1px solid #ffffff;
	color:#E32636;
}
btn.light-btn:hover, btn.light-btn:focus, a.light-btn:hover, a.light-btn:focus{
	background:#E32636;
	border:1px solid #E32636;
	color:#ffffff;
}
a.btn.contact-btn {
    background: rgba(255,255,255,0.1);
    color: #ffffff;
}
a.btn.contact-btn:hover, a.btn.contact-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#E32636;	
}
a.btn.contact-btn:hover i, a.btn.contact-btn:focus i{
	color:#E32636;
}
a.btn.listing-btn:hover, a.btn.listing-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#E32636;
}
a.btn.listing-btn:hover i, a.btn.listing-btn:focus i{
	color:#E32636;
}
a.btn.listing-btn {
    background:#E32636;
    border: 1px solid #E32636;
    color: #ffffff;
}

.listing-shot-info.rating a.detail-link {
    color:#E32636;
}
.title-content a{
	color:#E32636;
}

.detail-wrapper-body ul.detail-check li:before {
    background-color:#E32636;
}
ul.side-list-inline.social-side li a :hover, ul.side-list-inline.social-side li a :focus {
    background:#E32636;
    color: #ffffff;
}
.btn.counter-btn:hover, .btn.counter-btn:focus {
    background:#E32636;
    color: #ffffff;
}
/*---pagination--*/
.pagination li:first-child a {
    background:#E32636;
    border: 1px solid #E32636;
}
.pagination>.active>a, .pagination>.active>span, .pagination>.active>a:hover, .pagination>.active>span:hover, .pagination>.active>a:focus, .pagination>.active>span:focus, .pagination>li>a:hover, .pagination>li>a:focus {
    color:#E32636;
    background-color: rgba(227, 38, 54,0.12);
    border-color:#E32636;
}
.verticleilist.listing-shot span.like-listing i {
    color: #ff4e64;
}
span.like-listing i {
    background:#E32636;
    border: 1px solid transparent;
}
.layout-option a.active {
    color:#E32636;
}
.layout-option a:hover, .layout-option a:focus {
    color:#E32636;
}
.edit-info .btn {
    border: 1px solid #E32636;
    color:#E32636;
}
.custom-checkbox input[type="checkbox"]:checked + label:before {
	border-color:#E32636;
	background:#E32636;
}
ul.social-info.info-list li i {
    color:#E32636;
}
.cover-buttons .btn-outlined:hover, .cover-buttons .btn-outlined:focus {
    color:#E32636;
    background: #ffffff;
    border: 1px solid #ffffff;
}
/*---Testimonial---*/
.testimonials-2 .testimonial-detail .testimonial-title {
    color:#E32636;
}
.testimonials-2 .owl-theme .owl-controls .owl-page.active span, .testimonials-2 .owl-theme .owl-controls.clickable .owl-page:hover span {
    border: 4px solid #E32636;
}
.testimonials-2 .owl-theme .owl-controls .owl-page span {
    border: 2px solid #E32636;
}
/*-----Accordion Style -----*/
#accordion .panel-title a:after, #accordion .panel-title a.collapsed:after {
    color:#E32636;
}
#accordion2 .panel-title a:after, #accordion2 .panel-title a.collapsed:after {

    color:#E32636;
}
#accordion2.panel-group.style-2 .panel-title a.collapsed {
    color: #ffffff;
    background:#E32636;
}
/*---Tab Style---*/
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color:#E32636;
    border-bottom: 2px solid #E32636;
}
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color:#E32636;
    border-bottom: 2px solid #E32636;
}
.tab.style-2 .nav-tabs li a:hover, .tab.style-2 .nav-tabs li.active a {
    color: #ffffff;
    border-bottom: 2px solid #E32636;
    background:#E32636;
}

.footer-copyright p a {
    color: #E32636;
}
.footer-social li a:hover i {
    background: #E32636;
    color: #ffffff;
}
.verticleilist.listing-shot:hover span.like-listing i, .verticleilist.listing-shot:focus span.like-listing i {
    background: #E32636;
    border: 1px solid #E32636;
}
.small-list-detail p a, p a {
    color: #E32636;
}
.quote-card::before {
    color:#E32636;
}
.quote-card cite {
    color:#E32636;
}
ol.check-listing > li:before, ul.check-listing > li:before {
    color: #E32636;
}
.service-box:before {
    border-left: 1px solid #E32636;
    border-right: 1px solid #E32636;
}
.service-box .read a:hover, .service-box:hover .service-icon i {
    color: #E32636;
}
.service-box:hover .service-icon i, .service-box:hover .service-content h3 a {
    color: #E32636;
}
.bootstrap-select.btn-group .dropdown-menu li a:hover {
    background: #E32636;
}
.dropdown-menu>.active>a, .dropdown-menu>.active>a:hover, .dropdown-menu>.active>a:focus {
    background-color:#E32636;
}
.service-box:after {
    border-bottom: 1px solid #E32636;
    border-top: 1px solid #E32636;
}
/*-----Radio button & Range Slider-------*/
.custom-radio [type="radio"]:checked + label:after,
.custom-radio [type="radio"]:not(:checked) + label:after {
    background:#E32636;
}
.range-slider .slider-selection {
    background:#E32636;
}
.range-slider .slider-handle.round {
    border: 2px solid #E32636;
}

@media only screen and (min-width: 1024px){
nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:hover, nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:focus {
    color: #E32636;
}
}
@media only screen and (min-width: 993px){
body nav.navbar.bootsnav.navbar-transparent ul.nav > li > a.addlist {
    background:#E32636 !important;
}
body nav.navbar.bootsnav ul.nav > li > a.addlist {
    background:#E32636 !important;
}
}