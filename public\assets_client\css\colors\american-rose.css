/*-----General Information----*/
nav.navbar.bootsnav ul.nav li.active>a, .navbar-default .navbar-nav>li>a:focus, .navbar-default .navbar-nav>li>a:hover {
    color: #ff033E !important;
}
.heading h2 span {
    color:#ff033E;
}
.theme-cl {
    color:#ff033E;
}
.theme-bg {
    background:#ff033E;
}
span.like-listing i {
    background:#ff033E;
    border: 1px solid transparent;
}
.theme-overlap:before {
    background:#ff033E;
}
.feature-box span {
    background:#ff033E;
}
.btn-default.active.focus, .btn-default.active:focus, .btn-default.active:hover, .btn-default:active.focus, .btn-default:active:focus, .btn-default:active:hover, .open>.dropdown-toggle.btn-default.focus, .open>.dropdown-toggle.btn-default:focus, .open>.dropdown-toggle.btn-default:hover {
    color: #fff;
    background-color:#ff033E;
    border-color:#ff033E;
}
/*---Category Colors-----*/
.category-boxs:hover .category-box-btn, .category-boxs:focus .category-box-btn {
    background:#ff033E;
    border-color:#ff033E;
}
.category-full-widget:hover .btn-btn-wrowse, .category-full-widget:focus .btn-btn-wrowse {
    background:#ff033E;
    color: #ffffff;
}
.category-box-full.style-2:hover .category-box-2 i, .category-box-full.style-2:focus .category-box-2 i{
	background:#ff033E;
	border-color:#ff033E;
}
.cat-box-name .btn-btn-wrowse:hover, .cat-box-name .btn-btn-wrowse:focus {
    background:#ff033E;
}
span.category-tag {
    color:#ff033E;
    border: 1px solid #ff033E;
}
/*---prices---*/
.active .package-header {
    background:#ff033E;
}
button.btn.btn-package {
    background:#ff033E;
}
/*----button colors---*/
.theme-btn {
    background:#ff033E;
    border: 1px solid #ff033E;
	color:#ffffff;
	text-transform:uppercase;
}
.theme-btn:hover, .theme-btn:focus{
	color:#ffffff;
	background:#ff033E;
    border: 1px solid #ff033E;
}
btn.theme-btn-outlined, a.theme-btn-outlined{
	background:transparent;
	border:1px solid #ff033E;
	color:#ffffff;	
}
btn.theme-btn-outlined:hover, a.theme-btn-outlined:hover, btn.theme-btn-outlined:focus, a.theme-btn-outlined:focus{
	background:#ff033E;
	border-color:#ff033E;
	color:#ffffff;
}
btn.theme-btn-trans, a.theme-btn-trans{
	background: rgba(255, 3, 62,0.1);
    color:#ff033E;
    border-radius: 50px;
    border: 1px solid #ff033E;
}
btn.theme-btn-trans:hover, a.theme-btn-trans:hover, btn.theme-btn-trans:focus, a.theme-btn-trans:focus{
	background: rgba(255, 3, 62,1);
    color: #ffffff;
    border-radius: 50px;
    border: 1px solid #ff033E;
}
btn.btn-light-outlined, a.btn-light-outlined{
	background:rgba(255,255,255,0.1);
	border:1px solid #ffffff;
	color:#ffffff;
}
btn.btn-light-outlined:hover, a.btn-light-outlined:hover, btn.btn-light-outlined:focus, a.btn-light-outlined:focus{
	background:rgba(255,255,255,1);
	border:1px solid #ffffff;
	color:#ff033E;
}
btn.light-btn, a.light-btn{
	background:#ffffff;
	border:1px solid #ffffff;
	color:#ff033E;
}
btn.light-btn:hover, btn.light-btn:focus, a.light-btn:hover, a.light-btn:focus{
	background:#ff033E;
	border:1px solid #ff033E;
	color:#ffffff;
}
a.btn.contact-btn {
    background: rgba(255,255,255,0.1);
    color: #ffffff;
}
a.btn.contact-btn:hover, a.btn.contact-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#ff033E;	
}
a.btn.contact-btn:hover i, a.btn.contact-btn:focus i{
	color:#ff033E;
}
a.btn.listing-btn:hover, a.btn.listing-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#ff033E;
}
a.btn.listing-btn:hover i, a.btn.listing-btn:focus i{
	color:#ff033E;
}
a.btn.listing-btn {
    background:#ff033E;
    border: 1px solid #ff033E;
    color: #ffffff;
}

.listing-shot-info.rating a.detail-link {
    color:#ff033E;
}
.title-content a{
	color:#ff033E;
}

.detail-wrapper-body ul.detail-check li:before {
    background-color:#ff033E;
}
ul.side-list-inline.social-side li a :hover, ul.side-list-inline.social-side li a :focus {
    background:#ff033E;
    color: #ffffff;
}
.btn.counter-btn:hover, .btn.counter-btn:focus {
    background:#ff033E;
    color: #ffffff;
}
/*---pagination--*/
.pagination li:first-child a {
    background:#ff033E;
    border: 1px solid #ff033E;
}
.pagination>.active>a, .pagination>.active>span, .pagination>.active>a:hover, .pagination>.active>span:hover, .pagination>.active>a:focus, .pagination>.active>span:focus, .pagination>li>a:hover, .pagination>li>a:focus {
    color:#ff033E;
    background-color: rgba(255, 3, 62,0.12);
    border-color:#ff033E;
}
.verticleilist.listing-shot span.like-listing i {
    color: #ff4e64;
}
span.like-listing i {
    background:#ff033E;
    border: 1px solid transparent;
}
.layout-option a.active {
    color:#ff033E;
}
.layout-option a:hover, .layout-option a:focus {
    color:#ff033E;
}
.edit-info .btn {
    border: 1px solid #ff033E;
    color:#ff033E;
}
.custom-checkbox input[type="checkbox"]:checked + label:before {
	border-color:#ff033E;
	background:#ff033E;
}
ul.social-info.info-list li i {
    color:#ff033E;
}
.cover-buttons .btn-outlined:hover, .cover-buttons .btn-outlined:focus {
    color:#ff033E;
    background: #ffffff;
    border: 1px solid #ffffff;
}
/*---Testimonial---*/
.testimonials-2 .testimonial-detail .testimonial-title {
    color:#ff033E;
}
.testimonials-2 .owl-theme .owl-controls .owl-page.active span, .testimonials-2 .owl-theme .owl-controls.clickable .owl-page:hover span {
    border: 4px solid #ff033E;
}
.testimonials-2 .owl-theme .owl-controls .owl-page span {
    border: 2px solid #ff033E;
}
/*-----Accordion Style -----*/
#accordion .panel-title a:after, #accordion .panel-title a.collapsed:after {
    color:#ff033E;
}
#accordion2 .panel-title a:after, #accordion2 .panel-title a.collapsed:after {

    color:#ff033E;
}
#accordion2.panel-group.style-2 .panel-title a.collapsed {
    color: #ffffff;
    background:#ff033E;
}
/*---Tab Style---*/
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color:#ff033E;
    border-bottom: 2px solid #ff033E;
}
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color:#ff033E;
    border-bottom: 2px solid #ff033E;
}
.tab.style-2 .nav-tabs li a:hover, .tab.style-2 .nav-tabs li.active a {
    color: #ffffff;
    border-bottom: 2px solid #ff033E;
    background:#ff033E;
}

.footer-copyright p a {
    color: #ff033E;
}
.footer-social li a:hover i {
    background: #ff033E;
    color: #ffffff;
}
.verticleilist.listing-shot:hover span.like-listing i, .verticleilist.listing-shot:focus span.like-listing i {
    background: #ff033E;
    border: 1px solid #ff033E;
}
.small-list-detail p a, p a {
    color: #ff033E;
}
.quote-card::before {
    color:#ff033E;
}
.quote-card cite {
    color:#ff033E;
}
ol.check-listing > li:before, ul.check-listing > li:before {
    color: #ff033E;
}
.service-box:before {
    border-left: 1px solid #ff033E;
    border-right: 1px solid #ff033E;
}
.service-box .read a:hover, .service-box:hover .service-icon i {
    color: #ff033E;
}
.service-box:hover .service-icon i, .service-box:hover .service-content h3 a {
    color: #ff033E;
}
.bootstrap-select.btn-group .dropdown-menu li a:hover {
    background: #ff033E;
}
.dropdown-menu>.active>a, .dropdown-menu>.active>a:hover, .dropdown-menu>.active>a:focus {
    background-color:#ff033E;
}
.service-box:after {
    border-bottom: 1px solid #ff033E;
    border-top: 1px solid #ff033E;
}
/*-----Radio button & Range Slider-------*/
.custom-radio [type="radio"]:checked + label:after,
.custom-radio [type="radio"]:not(:checked) + label:after {
    background:#ff033E;
}
.range-slider .slider-selection {
    background:#ff033E;
}
.range-slider .slider-handle.round {
    border: 2px solid #ff033E;
}

@media only screen and (min-width: 1024px){
nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:hover, nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:focus {
    color: #ff033E;
}
}
@media only screen and (min-width: 993px){
body nav.navbar.bootsnav.navbar-transparent ul.nav > li > a.addlist {
    background:#ff033E !important;
}
body nav.navbar.bootsnav ul.nav > li > a.addlist {
    background:#ff033E !important;
}
}