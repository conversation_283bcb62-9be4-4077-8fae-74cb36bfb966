/*-----General Information----*/
nav.navbar.bootsnav ul.nav li.active>a, .navbar-default .navbar-nav>li>a:focus, .navbar-default .navbar-nav>li>a:hover {
    color: #0d98ba !important;
}
.heading h2 span {
    color:#0d98ba;
}
.theme-cl {
    color:#0d98ba;
}
.theme-bg {
    background:#0d98ba;
}
span.like-listing i {
    background:#0d98ba;
    border: 1px solid transparent;
}
.theme-overlap:before {
    background:#0d98ba;
}
.feature-box span {
    background:#0d98ba;
}
.btn-default.active.focus, .btn-default.active:focus, .btn-default.active:hover, .btn-default:active.focus, .btn-default:active:focus, .btn-default:active:hover, .open>.dropdown-toggle.btn-default.focus, .open>.dropdown-toggle.btn-default:focus, .open>.dropdown-toggle.btn-default:hover {
    color: #fff;
    background-color:#0d98ba;
    border-color:#0d98ba;
}
/*---Category Colors-----*/
.category-boxs:hover .category-box-btn, .category-boxs:focus .category-box-btn {
    background:#0d98ba;
    border-color:#0d98ba;
}
.category-full-widget:hover .btn-btn-wrowse, .category-full-widget:focus .btn-btn-wrowse {
    background:#0d98ba;
    color: #ffffff;
}
.category-box-full.style-2:hover .category-box-2 i, .category-box-full.style-2:focus .category-box-2 i{
	background:#0d98ba;
	border-color:#0d98ba;
}
.cat-box-name .btn-btn-wrowse:hover, .cat-box-name .btn-btn-wrowse:focus {
    background:#0d98ba;
}
span.category-tag {
    color:#0d98ba;
    border: 1px solid #0d98ba;
}
/*---prices---*/
.active .package-header {
    background:#0d98ba;
}
button.btn.btn-package {
    background:#0d98ba;
}
/*----button colors---*/
.theme-btn {
    background:#0d98ba;
    border: 1px solid #0d98ba;
	color:#ffffff;
	text-transform:uppercase;
}
.theme-btn:hover, .theme-btn:focus{
	color:#ffffff;
	background:#0d98ba;
    border: 1px solid #0d98ba;
}
btn.theme-btn-outlined, a.theme-btn-outlined{
	background:transparent;
	border:1px solid #0d98ba;
	color:#ffffff;	
}
btn.theme-btn-outlined:hover, a.theme-btn-outlined:hover, btn.theme-btn-outlined:focus, a.theme-btn-outlined:focus{
	background:#0d98ba;
	border-color:#0d98ba;
	color:#ffffff;
}
btn.theme-btn-trans, a.theme-btn-trans{
	background: rgba(13, 152, 186,0.1);
    color:#0d98ba;
    border-radius: 50px;
    border: 1px solid #0d98ba;
}
btn.theme-btn-trans:hover, a.theme-btn-trans:hover, btn.theme-btn-trans:focus, a.theme-btn-trans:focus{
	background: rgba(13, 152, 186,1);
    color: #ffffff;
    border-radius: 50px;
    border: 1px solid #0d98ba;
}
btn.btn-light-outlined, a.btn-light-outlined{
	background:rgba(255,255,255,0.1);
	border:1px solid #ffffff;
	color:#ffffff;
}
btn.btn-light-outlined:hover, a.btn-light-outlined:hover, btn.btn-light-outlined:focus, a.btn-light-outlined:focus{
	background:rgba(255,255,255,1);
	border:1px solid #ffffff;
	color:#0d98ba;
}
btn.light-btn, a.light-btn{
	background:#ffffff;
	border:1px solid #ffffff;
	color:#0d98ba;
}
btn.light-btn:hover, btn.light-btn:focus, a.light-btn:hover, a.light-btn:focus{
	background:#0d98ba;
	border:1px solid #0d98ba;
	color:#ffffff;
}
a.btn.contact-btn {
    background: rgba(255,255,255,0.1);
    color: #ffffff;
}
a.btn.contact-btn:hover, a.btn.contact-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#0d98ba;	
}
a.btn.contact-btn:hover i, a.btn.contact-btn:focus i{
	color:#0d98ba;
}
a.btn.listing-btn:hover, a.btn.listing-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#0d98ba;
}
a.btn.listing-btn:hover i, a.btn.listing-btn:focus i{
	color:#0d98ba;
}
a.btn.listing-btn {
    background:#0d98ba;
    border: 1px solid #0d98ba;
    color: #ffffff;
}

.listing-shot-info.rating a.detail-link {
    color:#0d98ba;
}
.title-content a{
	color:#0d98ba;
}

.detail-wrapper-body ul.detail-check li:before {
    background-color:#0d98ba;
}
ul.side-list-inline.social-side li a :hover, ul.side-list-inline.social-side li a :focus {
    background:#0d98ba;
    color: #ffffff;
}
.btn.counter-btn:hover, .btn.counter-btn:focus {
    background:#0d98ba;
    color: #ffffff;
}
/*---pagination--*/
.pagination li:first-child a {
    background:#0d98ba;
    border: 1px solid #0d98ba;
}
.pagination>.active>a, .pagination>.active>span, .pagination>.active>a:hover, .pagination>.active>span:hover, .pagination>.active>a:focus, .pagination>.active>span:focus, .pagination>li>a:hover, .pagination>li>a:focus {
    color:#0d98ba;
    background-color: rgba(13, 152, 186,0.12);
    border-color:#0d98ba;
}
.verticleilist.listing-shot span.like-listing i {
    color: #ff4e64;
}
span.like-listing i {
    background:#0d98ba;
    border: 1px solid transparent;
}
.layout-option a.active {
    color:#0d98ba;
}
.layout-option a:hover, .layout-option a:focus {
    color:#0d98ba;
}
.edit-info .btn {
    border: 1px solid #0d98ba;
    color:#0d98ba;
}
.custom-checkbox input[type="checkbox"]:checked + label:before {
	border-color:#0d98ba;
	background:#0d98ba;
}
ul.social-info.info-list li i {
    color:#0d98ba;
}
.cover-buttons .btn-outlined:hover, .cover-buttons .btn-outlined:focus {
    color:#0d98ba;
    background: #ffffff;
    border: 1px solid #ffffff;
}
/*---Testimonial---*/
.testimonials-2 .testimonial-detail .testimonial-title {
    color:#0d98ba;
}
.testimonials-2 .owl-theme .owl-controls .owl-page.active span, .testimonials-2 .owl-theme .owl-controls.clickable .owl-page:hover span {
    border: 4px solid #0d98ba;
}
.testimonials-2 .owl-theme .owl-controls .owl-page span {
    border: 2px solid #0d98ba;
}
/*-----Accordion Style -----*/
#accordion .panel-title a:after, #accordion .panel-title a.collapsed:after {
    color:#0d98ba;
}
#accordion2 .panel-title a:after, #accordion2 .panel-title a.collapsed:after {

    color:#0d98ba;
}
#accordion2.panel-group.style-2 .panel-title a.collapsed {
    color: #ffffff;
    background:#0d98ba;
}
/*---Tab Style---*/
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color:#0d98ba;
    border-bottom: 2px solid #0d98ba;
}
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color:#0d98ba;
    border-bottom: 2px solid #0d98ba;
}
.tab.style-2 .nav-tabs li a:hover, .tab.style-2 .nav-tabs li.active a {
    color: #ffffff;
    border-bottom: 2px solid #0d98ba;
    background:#0d98ba;
}

.footer-copyright p a {
    color: #0d98ba;
}
.footer-social li a:hover i {
    background: #0d98ba;
    color: #ffffff;
}
.verticleilist.listing-shot:hover span.like-listing i, .verticleilist.listing-shot:focus span.like-listing i {
    background: #0d98ba;
    border: 1px solid #0d98ba;
}
.small-list-detail p a, p a {
    color: #0d98ba;
}
.quote-card::before {
    color:#0d98ba;
}
.quote-card cite {
    color:#0d98ba;
}
ol.check-listing > li:before, ul.check-listing > li:before {
    color: #0d98ba;
}
.service-box:before {
    border-left: 1px solid #0d98ba;
    border-right: 1px solid #0d98ba;
}
.service-box .read a:hover, .service-box:hover .service-icon i {
    color: #0d98ba;
}
.service-box:hover .service-icon i, .service-box:hover .service-content h3 a {
    color: #0d98ba;
}
.bootstrap-select.btn-group .dropdown-menu li a:hover {
    background: #0d98ba;
}
.dropdown-menu>.active>a, .dropdown-menu>.active>a:hover, .dropdown-menu>.active>a:focus {
    background-color:#0d98ba;
}
.service-box:after {
    border-bottom: 1px solid #0d98ba;
    border-top: 1px solid #0d98ba;
}
/*-----Radio button & Range Slider-------*/
.custom-radio [type="radio"]:checked + label:after,
.custom-radio [type="radio"]:not(:checked) + label:after {
    background:#0d98ba;
}
.range-slider .slider-selection {
    background:#0d98ba;
}
.range-slider .slider-handle.round {
    border: 2px solid #0d98ba;
}

@media only screen and (min-width: 1024px){
nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:hover, nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:focus {
    color: #0d98ba;
}
}
@media only screen and (min-width: 993px){
body nav.navbar.bootsnav.navbar-transparent ul.nav > li > a.addlist {
    background:#0d98ba !important;
}
body nav.navbar.bootsnav ul.nav > li > a.addlist {
    background:#0d98ba !important;
}
}