/*-----General Information----*/
nav.navbar.bootsnav ul.nav li.active>a, .navbar-default .navbar-nav>li>a:focus, .navbar-default .navbar-nav>li>a:hover {
    color: #36454f !important;
}
.heading h2 span {
    color:#36454f;
}
.theme-cl {
    color:#36454f;
}
.theme-bg {
    background:#36454f;
}
span.like-listing i {
    background:#36454f;
    border: 1px solid transparent;
}
.theme-overlap:before {
    background:#36454f;
}
.feature-box span {
    background:#36454f;
}
.btn-default.active.focus, .btn-default.active:focus, .btn-default.active:hover, .btn-default:active.focus, .btn-default:active:focus, .btn-default:active:hover, .open>.dropdown-toggle.btn-default.focus, .open>.dropdown-toggle.btn-default:focus, .open>.dropdown-toggle.btn-default:hover {
    color: #fff;
    background-color:#36454f;
    border-color:#36454f;
}
/*---Category Colors-----*/
.category-boxs:hover .category-box-btn, .category-boxs:focus .category-box-btn {
    background:#36454f;
    border-color:#36454f;
}
.category-full-widget:hover .btn-btn-wrowse, .category-full-widget:focus .btn-btn-wrowse {
    background:#36454f;
    color: #ffffff;
}
.category-box-full.style-2:hover .category-box-2 i, .category-box-full.style-2:focus .category-box-2 i{
	background:#36454f;
	border-color:#36454f;
}
.cat-box-name .btn-btn-wrowse:hover, .cat-box-name .btn-btn-wrowse:focus {
    background:#36454f;
}
span.category-tag {
    color:#36454f;
    border: 1px solid #36454f;
}
/*---prices---*/
.active .package-header {
    background:#36454f;
}
button.btn.btn-package {
    background:#36454f;
}
/*----button colors---*/
.theme-btn {
    background:#36454f;
    border: 1px solid #36454f;
	color:#ffffff;
	text-transform:uppercase;
}
.theme-btn:hover, .theme-btn:focus{
	color:#ffffff;
	background:#36454f;
    border: 1px solid #36454f;
}
btn.theme-btn-outlined, a.theme-btn-outlined{
	background:transparent;
	border:1px solid #36454f;
	color:#ffffff;	
}
btn.theme-btn-outlined:hover, a.theme-btn-outlined:hover, btn.theme-btn-outlined:focus, a.theme-btn-outlined:focus{
	background:#36454f;
	border-color:#36454f;
	color:#ffffff;
}
btn.theme-btn-trans, a.theme-btn-trans{
	background: rgba(54, 69, 79,0.1);
    color:#36454f;
    border-radius: 50px;
    border: 1px solid #36454f;
}
btn.theme-btn-trans:hover, a.theme-btn-trans:hover, btn.theme-btn-trans:focus, a.theme-btn-trans:focus{
	background: rgba(54, 69, 79,1);
    color: #ffffff;
    border-radius: 50px;
    border: 1px solid #36454f;
}
btn.btn-light-outlined, a.btn-light-outlined{
	background:rgba(255,255,255,0.1);
	border:1px solid #ffffff;
	color:#ffffff;
}
btn.btn-light-outlined:hover, a.btn-light-outlined:hover, btn.btn-light-outlined:focus, a.btn-light-outlined:focus{
	background:rgba(255,255,255,1);
	border:1px solid #ffffff;
	color:#36454f;
}
btn.light-btn, a.light-btn{
	background:#ffffff;
	border:1px solid #ffffff;
	color:#36454f;
}
btn.light-btn:hover, btn.light-btn:focus, a.light-btn:hover, a.light-btn:focus{
	background:#36454f;
	border:1px solid #36454f;
	color:#ffffff;
}
a.btn.contact-btn {
    background: rgba(255,255,255,0.1);
    color: #ffffff;
}
a.btn.contact-btn:hover, a.btn.contact-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#36454f;	
}
a.btn.contact-btn:hover i, a.btn.contact-btn:focus i{
	color:#36454f;
}
a.btn.listing-btn:hover, a.btn.listing-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#36454f;
}
a.btn.listing-btn:hover i, a.btn.listing-btn:focus i{
	color:#36454f;
}
a.btn.listing-btn {
    background:#36454f;
    border: 1px solid #36454f;
    color: #ffffff;
}

.listing-shot-info.rating a.detail-link {
    color:#36454f;
}
.title-content a{
	color:#36454f;
}

.detail-wrapper-body ul.detail-check li:before {
    background-color:#36454f;
}
ul.side-list-inline.social-side li a :hover, ul.side-list-inline.social-side li a :focus {
    background:#36454f;
    color: #ffffff;
}
.btn.counter-btn:hover, .btn.counter-btn:focus {
    background:#36454f;
    color: #ffffff;
}
/*---pagination--*/
.pagination li:first-child a {
    background:#36454f;
    border: 1px solid #36454f;
}
.pagination>.active>a, .pagination>.active>span, .pagination>.active>a:hover, .pagination>.active>span:hover, .pagination>.active>a:focus, .pagination>.active>span:focus, .pagination>li>a:hover, .pagination>li>a:focus {
    color:#36454f;
    background-color: rgba(54, 69, 79,0.12);
    border-color:#36454f;
}
.verticleilist.listing-shot span.like-listing i {
    color: #ff4e64;
}
span.like-listing i {
    background:#36454f;
    border: 1px solid transparent;
}
.layout-option a.active {
    color:#36454f;
}
.layout-option a:hover, .layout-option a:focus {
    color:#36454f;
}
.edit-info .btn {
    border: 1px solid #36454f;
    color:#36454f;
}
.custom-checkbox input[type="checkbox"]:checked + label:before {
	border-color:#36454f;
	background:#36454f;
}
ul.social-info.info-list li i {
    color:#36454f;
}
.cover-buttons .btn-outlined:hover, .cover-buttons .btn-outlined:focus {
    color:#36454f;
    background: #ffffff;
    border: 1px solid #ffffff;
}
/*---Testimonial---*/
.testimonials-2 .testimonial-detail .testimonial-title {
    color:#36454f;
}
.testimonials-2 .owl-theme .owl-controls .owl-page.active span, .testimonials-2 .owl-theme .owl-controls.clickable .owl-page:hover span {
    border: 4px solid #36454f;
}
.testimonials-2 .owl-theme .owl-controls .owl-page span {
    border: 2px solid #36454f;
}
/*-----Accordion Style -----*/
#accordion .panel-title a:after, #accordion .panel-title a.collapsed:after {
    color:#36454f;
}
#accordion2 .panel-title a:after, #accordion2 .panel-title a.collapsed:after {

    color:#36454f;
}
#accordion2.panel-group.style-2 .panel-title a.collapsed {
    color: #ffffff;
    background:#36454f;
}
/*---Tab Style---*/
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color:#36454f;
    border-bottom: 2px solid #36454f;
}
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color:#36454f;
    border-bottom: 2px solid #36454f;
}
.tab.style-2 .nav-tabs li a:hover, .tab.style-2 .nav-tabs li.active a {
    color: #ffffff;
    border-bottom: 2px solid #36454f;
    background:#36454f;
}

.footer-copyright p a {
    color: #36454f;
}
.footer-social li a:hover i {
    background: #36454f;
    color: #ffffff;
}
.verticleilist.listing-shot:hover span.like-listing i, .verticleilist.listing-shot:focus span.like-listing i {
    background: #36454f;
    border: 1px solid #36454f;
}
.small-list-detail p a, p a {
    color: #36454f;
}
.quote-card::before {
    color:#36454f;
}
.quote-card cite {
    color:#36454f;
}
ol.check-listing > li:before, ul.check-listing > li:before {
    color: #36454f;
}
.service-box:before {
    border-left: 1px solid #36454f;
    border-right: 1px solid #36454f;
}
.service-box .read a:hover, .service-box:hover .service-icon i {
    color: #36454f;
}
.service-box:hover .service-icon i, .service-box:hover .service-content h3 a {
    color: #36454f;
}
.bootstrap-select.btn-group .dropdown-menu li a:hover {
    background: #36454f;
}
.dropdown-menu>.active>a, .dropdown-menu>.active>a:hover, .dropdown-menu>.active>a:focus {
    background-color:#36454f;
}
.service-box:after {
    border-bottom: 1px solid #36454f;
    border-top: 1px solid #36454f;
}
/*-----Radio button & Range Slider-------*/
.custom-radio [type="radio"]:checked + label:after,
.custom-radio [type="radio"]:not(:checked) + label:after {
    background:#36454f;
}
.range-slider .slider-selection {
    background:#36454f;
}
.range-slider .slider-handle.round {
    border: 2px solid #36454f;
}

@media only screen and (min-width: 1024px){
nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:hover, nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:focus {
    color: #36454f;
}
}
@media only screen and (min-width: 993px){
body nav.navbar.bootsnav.navbar-transparent ul.nav > li > a.addlist {
    background:#36454f !important;
}
body nav.navbar.bootsnav ul.nav > li > a.addlist {
    background:#36454f !important;
}
}