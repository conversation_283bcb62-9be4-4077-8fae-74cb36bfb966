/*-----General Information----*/
nav.navbar.bootsnav ul.nav li.active>a, .navbar-default .navbar-nav>li>a:focus, .navbar-default .navbar-nav>li>a:hover {
    color: #008b8b !important;
}
.heading h2 span {
    color:#008b8b;
}
.theme-cl {
    color:#008b8b;
}
.theme-bg {
    background:#008b8b;
}
span.like-listing i {
    background:#008b8b;
    border: 1px solid transparent;
}
.theme-overlap:before {
    background:#008b8b;
}
.feature-box span {
    background:#008b8b;
}
.btn-default.active.focus, .btn-default.active:focus, .btn-default.active:hover, .btn-default:active.focus, .btn-default:active:focus, .btn-default:active:hover, .open>.dropdown-toggle.btn-default.focus, .open>.dropdown-toggle.btn-default:focus, .open>.dropdown-toggle.btn-default:hover {
    color: #fff;
    background-color:#008b8b;
    border-color:#008b8b;
}
/*---Category Colors-----*/
.category-boxs:hover .category-box-btn, .category-boxs:focus .category-box-btn {
    background:#008b8b;
    border-color:#008b8b;
}
.category-full-widget:hover .btn-btn-wrowse, .category-full-widget:focus .btn-btn-wrowse {
    background:#008b8b;
    color: #ffffff;
}
.category-box-full.style-2:hover .category-box-2 i, .category-box-full.style-2:focus .category-box-2 i{
	background:#008b8b;
	border-color:#008b8b;
}
.cat-box-name .btn-btn-wrowse:hover, .cat-box-name .btn-btn-wrowse:focus {
    background:#008b8b;
}
span.category-tag {
    color:#008b8b;
    border: 1px solid #008b8b;
}
/*---prices---*/
.active .package-header {
    background:#008b8b;
}
button.btn.btn-package {
    background:#008b8b;
}
/*----button colors---*/
.theme-btn {
    background:#008b8b;
    border: 1px solid #008b8b;
	color:#ffffff;
	text-transform:uppercase;
}
.theme-btn:hover, .theme-btn:focus{
	color:#ffffff;
	background:#008b8b;
    border: 1px solid #008b8b;
}
btn.theme-btn-outlined, a.theme-btn-outlined{
	background:transparent;
	border:1px solid #008b8b;
	color:#ffffff;	
}
btn.theme-btn-outlined:hover, a.theme-btn-outlined:hover, btn.theme-btn-outlined:focus, a.theme-btn-outlined:focus{
	background:#008b8b;
	border-color:#008b8b;
	color:#ffffff;
}
btn.theme-btn-trans, a.theme-btn-trans{
	background: rgba(0, 139, 139,0.1);
    color:#008b8b;
    border-radius: 50px;
    border: 1px solid #008b8b;
}
btn.theme-btn-trans:hover, a.theme-btn-trans:hover, btn.theme-btn-trans:focus, a.theme-btn-trans:focus{
	background: rgba(0, 139, 139,1);
    color: #ffffff;
    border-radius: 50px;
    border: 1px solid #008b8b;
}
btn.btn-light-outlined, a.btn-light-outlined{
	background:rgba(255,255,255,0.1);
	border:1px solid #ffffff;
	color:#ffffff;
}
btn.btn-light-outlined:hover, a.btn-light-outlined:hover, btn.btn-light-outlined:focus, a.btn-light-outlined:focus{
	background:rgba(255,255,255,1);
	border:1px solid #ffffff;
	color:#008b8b;
}
btn.light-btn, a.light-btn{
	background:#ffffff;
	border:1px solid #ffffff;
	color:#008b8b;
}
btn.light-btn:hover, btn.light-btn:focus, a.light-btn:hover, a.light-btn:focus{
	background:#008b8b;
	border:1px solid #008b8b;
	color:#ffffff;
}
a.btn.contact-btn {
    background: rgba(255,255,255,0.1);
    color: #ffffff;
}
a.btn.contact-btn:hover, a.btn.contact-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#008b8b;	
}
a.btn.contact-btn:hover i, a.btn.contact-btn:focus i{
	color:#008b8b;
}
a.btn.listing-btn:hover, a.btn.listing-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#008b8b;
}
a.btn.listing-btn:hover i, a.btn.listing-btn:focus i{
	color:#008b8b;
}
a.btn.listing-btn {
    background:#008b8b;
    border: 1px solid #008b8b;
    color: #ffffff;
}

.listing-shot-info.rating a.detail-link {
    color:#008b8b;
}
.title-content a{
	color:#008b8b;
}

.detail-wrapper-body ul.detail-check li:before {
    background-color:#008b8b;
}
ul.side-list-inline.social-side li a :hover, ul.side-list-inline.social-side li a :focus {
    background:#008b8b;
    color: #ffffff;
}
.btn.counter-btn:hover, .btn.counter-btn:focus {
    background:#008b8b;
    color: #ffffff;
}
/*---pagination--*/
.pagination li:first-child a {
    background:#008b8b;
    border: 1px solid #008b8b;
}
.pagination>.active>a, .pagination>.active>span, .pagination>.active>a:hover, .pagination>.active>span:hover, .pagination>.active>a:focus, .pagination>.active>span:focus, .pagination>li>a:hover, .pagination>li>a:focus {
    color:#008b8b;
    background-color: rgba(0, 139, 139,0.12);
    border-color:#008b8b;
}
.verticleilist.listing-shot span.like-listing i {
    color: #ff4e64;
}
span.like-listing i {
    background:#008b8b;
    border: 1px solid transparent;
}
.layout-option a.active {
    color:#008b8b;
}
.layout-option a:hover, .layout-option a:focus {
    color:#008b8b;
}
.edit-info .btn {
    border: 1px solid #008b8b;
    color:#008b8b;
}
.custom-checkbox input[type="checkbox"]:checked + label:before {
	border-color:#008b8b;
	background:#008b8b;
}
ul.social-info.info-list li i {
    color:#008b8b;
}
.cover-buttons .btn-outlined:hover, .cover-buttons .btn-outlined:focus {
    color:#008b8b;
    background: #ffffff;
    border: 1px solid #ffffff;
}
/*---Testimonial---*/
.testimonials-2 .testimonial-detail .testimonial-title {
    color:#008b8b;
}
.testimonials-2 .owl-theme .owl-controls .owl-page.active span, .testimonials-2 .owl-theme .owl-controls.clickable .owl-page:hover span {
    border: 4px solid #008b8b;
}
.testimonials-2 .owl-theme .owl-controls .owl-page span {
    border: 2px solid #008b8b;
}
/*-----Accordion Style -----*/
#accordion .panel-title a:after, #accordion .panel-title a.collapsed:after {
    color:#008b8b;
}
#accordion2 .panel-title a:after, #accordion2 .panel-title a.collapsed:after {

    color:#008b8b;
}
#accordion2.panel-group.style-2 .panel-title a.collapsed {
    color: #ffffff;
    background:#008b8b;
}
/*---Tab Style---*/
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color:#008b8b;
    border-bottom: 2px solid #008b8b;
}
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color:#008b8b;
    border-bottom: 2px solid #008b8b;
}
.tab.style-2 .nav-tabs li a:hover, .tab.style-2 .nav-tabs li.active a {
    color: #ffffff;
    border-bottom: 2px solid #008b8b;
    background:#008b8b;
}

.footer-copyright p a {
    color: #008b8b;
}
.footer-social li a:hover i {
    background: #008b8b;
    color: #ffffff;
}
.verticleilist.listing-shot:hover span.like-listing i, .verticleilist.listing-shot:focus span.like-listing i {
    background: #008b8b;
    border: 1px solid #008b8b;
}
.small-list-detail p a, p a {
    color: #008b8b;
}
.quote-card::before {
    color:#008b8b;
}
.quote-card cite {
    color:#008b8b;
}
ol.check-listing > li:before, ul.check-listing > li:before {
    color: #008b8b;
}
.service-box:before {
    border-left: 1px solid  #008b8b;
    border-right: 1px solid  #008b8b;
}
.service-box .read a:hover, .service-box:hover .service-icon i {
    color:  #008b8b;
}
.service-box:hover .service-icon i, .service-box:hover .service-content h3 a {
    color:  #008b8b;
}
.bootstrap-select.btn-group .dropdown-menu li a:hover {
    background:  #008b8b;
}
.dropdown-menu>.active>a, .dropdown-menu>.active>a:hover, .dropdown-menu>.active>a:focus {
    background-color: #008b8b;
}
.service-box:after {
    border-bottom: 1px solid #008b8b;
    border-top: 1px solid #008b8b;
}
/*-----Radio button & Range Slider-------*/
.custom-radio [type="radio"]:checked + label:after,
.custom-radio [type="radio"]:not(:checked) + label:after {
    background:#008b8b;
}
.range-slider .slider-selection {
    background:#008b8b;
}
.range-slider .slider-handle.round {
    border: 2px solid #008b8b;
}

@media only screen and (min-width: 1024px){
nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:hover, nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:focus {
    color: #008b8b;
}
}
@media only screen and (min-width: 993px){
body nav.navbar.bootsnav.navbar-transparent ul.nav > li > a.addlist {
    background:#008b8b !important;
}
body nav.navbar.bootsnav ul.nav > li > a.addlist {
    background:#008b8b !important;
}
}