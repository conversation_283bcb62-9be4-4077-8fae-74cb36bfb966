/*-----General Information----*/
nav.navbar.bootsnav ul.nav li.active>a, .navbar-default .navbar-nav>li>a:focus, .navbar-default .navbar-nav>li>a:hover {
    color: #ff431e !important;
}
.heading h2 span {
    color: #ff431e;
}
.theme-cl {
    color: #ff431e;
}
.theme-bg {
    background: #ff431e;
}
span.like-listing i {
    background: #ff431e;
    border: 1px solid transparent;
}
.theme-overlap:before {
    background: #ff431e;
}
.feature-box span {
    background: #ff431e;
}
.btn-default.active.focus, .btn-default.active:focus, .btn-default.active:hover, .btn-default:active.focus, .btn-default:active:focus, .btn-default:active:hover, .open>.dropdown-toggle.btn-default.focus, .open>.dropdown-toggle.btn-default:focus, .open>.dropdown-toggle.btn-default:hover {
    color: #fff;
    background-color:#ff431e;
    border-color:#ff431e;
}
/*---Category Colors-----*/
.category-boxs:hover .category-box-btn, .category-boxs:focus .category-box-btn {
    background: #ff431e;
    border-color: #ff431e;
}
.category-full-widget:hover .btn-btn-wrowse, .category-full-widget:focus .btn-btn-wrowse {
    background: #ff431e;
    color: #ffffff;
}
.category-box-full.style-2:hover .category-box-2 i, .category-box-full.style-2:focus .category-box-2 i{
	background:#ff431e;
	border-color:#ff431e;
}
.cat-box-name .btn-btn-wrowse:hover, .cat-box-name .btn-btn-wrowse:focus {
    background: #ff431e;
}
span.category-tag {
    color: #ff431e;
    border: 1px solid #ff431e;
}
/*---prices---*/
.active .package-header {
    background: #ff431e;
}
button.btn.btn-package {
    background: #ff431e;
}
/*----button colors---*/
.theme-btn {
    background: #ff431e;
    border: 1px solid #ff431e;
	color:#ffffff;
	text-transform:uppercase;
}
.theme-btn:hover, .theme-btn:focus{
	color:#ffffff;
	background: #ff431e;
    border: 1px solid #ff431e;
}
btn.theme-btn-outlined, a.theme-btn-outlined{
	background:transparent;
	border:1px solid #ff431e;
	color:#ffffff;	
}
btn.theme-btn-outlined:hover, a.theme-btn-outlined:hover, btn.theme-btn-outlined:focus, a.theme-btn-outlined:focus{
	background:#ff431e;
	border-color:#ff431e;
	color:#ffffff;
}
btn.theme-btn-trans, a.theme-btn-trans{
	background: rgba(255, 67, 30,0.1);
    color: #ff431e;
    border-radius: 50px;
    border: 1px solid #ff431e;
}
btn.theme-btn-trans:hover, a.theme-btn-trans:hover, btn.theme-btn-trans:focus, a.theme-btn-trans:focus{
	background: rgba(255, 67, 30,1);
    color: #ffffff;
    border-radius: 50px;
    border: 1px solid #ff431e;
}
btn.btn-light-outlined, a.btn-light-outlined{
	background:rgba(255,255,255,0.1);
	border:1px solid #ffffff;
	color:#ffffff;
}
btn.btn-light-outlined:hover, a.btn-light-outlined:hover, btn.btn-light-outlined:focus, a.btn-light-outlined:focus{
	background:rgba(255,255,255,1);
	border:1px solid #ffffff;
	color:#ff431e;
}
btn.light-btn, a.light-btn{
	background:#ffffff;
	border:1px solid #ffffff;
	color:#ff431e;
}
btn.light-btn:hover, btn.light-btn:focus, a.light-btn:hover, a.light-btn:focus{
	background:#ff431e;
	border:1px solid #ff431e;
	color:#ffffff;
}
a.btn.contact-btn {
    background: rgba(255,255,255,0.1);
    color: #ffffff;
}
a.btn.contact-btn:hover, a.btn.contact-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#ff431e;	
}
a.btn.contact-btn:hover i, a.btn.contact-btn:focus i{
	color:#ff431e;
}
a.btn.listing-btn:hover, a.btn.listing-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#ff431e;
}
a.btn.listing-btn:hover i, a.btn.listing-btn:focus i{
	color:#ff431e;
}
a.btn.listing-btn {
    background: #ff431e;
    border: 1px solid #ff431e;
    color: #ffffff;
}

.listing-shot-info.rating a.detail-link {
    color: #ff431e;
}
.title-content a{
	color:#ff431e;
}

.detail-wrapper-body ul.detail-check li:before {
    background-color: #ff431e;
}
ul.side-list-inline.social-side li a :hover, ul.side-list-inline.social-side li a :focus {
    background: #ff431e;
    color: #ffffff;
}
.btn.counter-btn:hover, .btn.counter-btn:focus {
    background: #ff431e;
    color: #ffffff;
}
/*---pagination--*/
.pagination li:first-child a {
    background: #ff431e;
    border: 1px solid #ff431e;
}
.pagination>.active>a, .pagination>.active>span, .pagination>.active>a:hover, .pagination>.active>span:hover, .pagination>.active>a:focus, .pagination>.active>span:focus, .pagination>li>a:hover, .pagination>li>a:focus {
    color: #ff431e;
    background-color: rgba(255, 67, 30,0.12);
    border-color: #ff431e;
}
.verticleilist.listing-shot span.like-listing i {
    color: #ff4e64;
}
span.like-listing i {
    background: #ff431e;
    border: 1px solid transparent;
}
.layout-option a.active {
    color: #ff431e;
}
.layout-option a:hover, .layout-option a:focus {
    color: #ff431e;
}
.edit-info .btn {
    border: 1px solid #ff431e;
    color: #ff431e;
}
.custom-checkbox input[type="checkbox"]:checked + label:before {
	border-color:#ff431e;
	background:#ff431e;
}
ul.social-info.info-list li i {
    color: #ff431e;
}
.cover-buttons .btn-outlined:hover, .cover-buttons .btn-outlined:focus {
    color: #ff431e;
    background: #ffffff;
    border: 1px solid #ffffff;
}
/*---Testimonial---*/
.testimonials-2 .testimonial-detail .testimonial-title {
    color: #ff431e;
}
.testimonials-2 .owl-theme .owl-controls .owl-page.active span, .testimonials-2 .owl-theme .owl-controls.clickable .owl-page:hover span {
    border: 4px solid #ff431e;
}
.testimonials-2 .owl-theme .owl-controls .owl-page span {
    border: 2px solid #ff431e;
}
/*-----Accordion Style -----*/
#accordion .panel-title a:after, #accordion .panel-title a.collapsed:after {
    color: #ff431e;
}
#accordion2 .panel-title a:after, #accordion2 .panel-title a.collapsed:after {

    color: #ff431e;
}
#accordion2.panel-group.style-2 .panel-title a.collapsed {
    color: #ffffff;
    background: #ff431e;
}
/*---Tab Style---*/
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color: #ff431e;
    border-bottom: 2px solid #ff431e;
}
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color: #ff431e;
    border-bottom: 2px solid #ff431e;
}
.tab.style-2 .nav-tabs li a:hover, .tab.style-2 .nav-tabs li.active a {
    color: #ffffff;
    border-bottom: 2px solid #ff431e;
    background: #ff431e;
}

.footer-copyright p a {
    color: #ff431e;
}
.footer-social li a:hover i {
    background: #ff431e;
    color: #ffffff;
}
.verticleilist.listing-shot:hover span.like-listing i, .verticleilist.listing-shot:focus span.like-listing i {
    background: #ff431e;
    border: 1px solid #ff431e;
}
.small-list-detail p a, p a {
    color: #ff431e;
}
.quote-card::before {
    color:#ff431e;
}
.quote-card cite {
    color:#ff431e;
}
ol.check-listing > li:before, ul.check-listing > li:before {
    color: #ff431e;
}
.service-box:before {
    border-left: 1px solid #ff431e;
    border-right: 1px solid #ff431e;
}
.service-box .read a:hover, .service-box:hover .service-icon i {
    color: #ff431e;
}
.service-box:hover .service-icon i, .service-box:hover .service-content h3 a {
    color: #ff431e;
}
.bootstrap-select.btn-group .dropdown-menu li a:hover {
    background: #ff431e;
}
.dropdown-menu>.active>a, .dropdown-menu>.active>a:hover, .dropdown-menu>.active>a:focus {
    background-color:#ff431e;
}
.service-box:after {
    border-bottom: 1px solid #ff431e;
    border-top: 1px solid #ff431e;
}
/*-----Radio button & Range Slider-------*/
.custom-radio [type="radio"]:checked + label:after,
.custom-radio [type="radio"]:not(:checked) + label:after {
    background:#ff431e;
}
.range-slider .slider-selection {
    background:#ff431e;
}
.range-slider .slider-handle.round {
    border: 2px solid #ff431e;
}

@media only screen and (min-width: 1024px){
nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:hover, nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:focus {
    color: #ff431e;
}
}
@media only screen and (min-width: 993px){
body nav.navbar.bootsnav.navbar-transparent ul.nav > li > a.addlist {
    background: #ff431e !important;
}
body nav.navbar.bootsnav ul.nav > li > a.addlist {
    background: #ff431e !important;
}
}