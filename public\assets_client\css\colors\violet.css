/*-----General Information----*/
nav.navbar.bootsnav ul.nav li.active>a, .navbar-default .navbar-nav>li>a:focus, .navbar-default .navbar-nav>li>a:hover {
    color: #EE82EE !important;
}
.heading h2 span {
    color:#EE82EE;
}
.theme-cl {
    color:#EE82EE;
}
.theme-bg {
    background:#EE82EE;
}
span.like-listing i {
    background:#EE82EE;
    border: 1px solid transparent;
}
.theme-overlap:before {
    background:#EE82EE;
}
.feature-box span {
    background:#EE82EE;
}
.btn-default.active.focus, .btn-default.active:focus, .btn-default.active:hover, .btn-default:active.focus, .btn-default:active:focus, .btn-default:active:hover, .open>.dropdown-toggle.btn-default.focus, .open>.dropdown-toggle.btn-default:focus, .open>.dropdown-toggle.btn-default:hover {
    color: #fff;
    background-color:#EE82EE;
    border-color:#EE82EE;
}
/*---Category Colors-----*/
.category-boxs:hover .category-box-btn, .category-boxs:focus .category-box-btn {
    background:#EE82EE;
    border-color:#EE82EE;
}
.category-full-widget:hover .btn-btn-wrowse, .category-full-widget:focus .btn-btn-wrowse {
    background:#EE82EE;
    color: #ffffff;
}
.category-box-full.style-2:hover .category-box-2 i, .category-box-full.style-2:focus .category-box-2 i{
	background:#EE82EE;
	border-color:#EE82EE;
}
.cat-box-name .btn-btn-wrowse:hover, .cat-box-name .btn-btn-wrowse:focus {
    background:#EE82EE;
}
span.category-tag {
    color:#EE82EE;
    border: 1px solid #EE82EE;
}
/*---prices---*/
.active .package-header {
    background:#EE82EE;
}
button.btn.btn-package {
    background:#EE82EE;
}
/*----button colors---*/
.theme-btn {
    background:#EE82EE;
    border: 1px solid #EE82EE;
	color:#ffffff;
	text-transform:uppercase;
}
.theme-btn:hover, .theme-btn:focus{
	color:#ffffff;
	background:#EE82EE;
    border: 1px solid #EE82EE;
}
btn.theme-btn-outlined, a.theme-btn-outlined{
	background:transparent;
	border:1px solid #EE82EE;
	color:#ffffff;	
}
btn.theme-btn-outlined:hover, a.theme-btn-outlined:hover, btn.theme-btn-outlined:focus, a.theme-btn-outlined:focus{
	background:#EE82EE;
	border-color:#EE82EE;
	color:#ffffff;
}
btn.theme-btn-trans, a.theme-btn-trans{
	background: rgba(238, 130, 238,0.1);
    color:#EE82EE;
    border-radius: 50px;
    border: 1px solid #EE82EE;
}
btn.theme-btn-trans:hover, a.theme-btn-trans:hover, btn.theme-btn-trans:focus, a.theme-btn-trans:focus{
	background: rgba(238, 130, 238,1);
    color: #ffffff;
    border-radius: 50px;
    border: 1px solid #EE82EE;
}
btn.btn-light-outlined, a.btn-light-outlined{
	background:rgba(255,255,255,0.1);
	border:1px solid #ffffff;
	color:#ffffff;
}
btn.btn-light-outlined:hover, a.btn-light-outlined:hover, btn.btn-light-outlined:focus, a.btn-light-outlined:focus{
	background:rgba(255,255,255,1);
	border:1px solid #ffffff;
	color:#EE82EE;
}
btn.light-btn, a.light-btn{
	background:#ffffff;
	border:1px solid #ffffff;
	color:#EE82EE;
}
btn.light-btn:hover, btn.light-btn:focus, a.light-btn:hover, a.light-btn:focus{
	background:#EE82EE;
	border:1px solid #EE82EE;
	color:#ffffff;
}
a.btn.contact-btn {
    background: rgba(255,255,255,0.1);
    color: #ffffff;
}
a.btn.contact-btn:hover, a.btn.contact-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#EE82EE;	
}
a.btn.contact-btn:hover i, a.btn.contact-btn:focus i{
	color:#EE82EE;
}
a.btn.listing-btn:hover, a.btn.listing-btn:focus{
	background:#ffffff;
	border-color:#ffffff;
	color:#EE82EE;
}
a.btn.listing-btn:hover i, a.btn.listing-btn:focus i{
	color:#EE82EE;
}
a.btn.listing-btn {
    background:#EE82EE;
    border: 1px solid #EE82EE;
    color: #ffffff;
}

.listing-shot-info.rating a.detail-link {
    color:#EE82EE;
}
.title-content a{
	color:#EE82EE;
}

.detail-wrapper-body ul.detail-check li:before {
    background-color:#EE82EE;
}
ul.side-list-inline.social-side li a :hover, ul.side-list-inline.social-side li a :focus {
    background:#EE82EE;
    color: #ffffff;
}
.btn.counter-btn:hover, .btn.counter-btn:focus {
    background:#EE82EE;
    color: #ffffff;
}
/*---pagination--*/
.pagination li:first-child a {
    background:#EE82EE;
    border: 1px solid #EE82EE;
}
.pagination>.active>a, .pagination>.active>span, .pagination>.active>a:hover, .pagination>.active>span:hover, .pagination>.active>a:focus, .pagination>.active>span:focus, .pagination>li>a:hover, .pagination>li>a:focus {
    color:#EE82EE;
    background-color: rgba(238, 130, 238,0.12);
    border-color:#EE82EE;
}
.verticleilist.listing-shot span.like-listing i {
    color: #ff4e64;
}
span.like-listing i {
    background:#EE82EE;
    border: 1px solid transparent;
}
.layout-option a.active {
    color:#EE82EE;
}
.layout-option a:hover, .layout-option a:focus {
    color:#EE82EE;
}
.edit-info .btn {
    border: 1px solid #EE82EE;
    color:#EE82EE;
}
.custom-checkbox input[type="checkbox"]:checked + label:before {
	border-color:#EE82EE;
	background:#EE82EE;
}
ul.social-info.info-list li i {
    color:#EE82EE;
}
.cover-buttons .btn-outlined:hover, .cover-buttons .btn-outlined:focus {
    color:#EE82EE;
    background: #ffffff;
    border: 1px solid #ffffff;
}
/*---Testimonial---*/
.testimonials-2 .testimonial-detail .testimonial-title {
    color:#EE82EE;
}
.testimonials-2 .owl-theme .owl-controls .owl-page.active span, .testimonials-2 .owl-theme .owl-controls.clickable .owl-page:hover span {
    border: 4px solid #EE82EE;
}
.testimonials-2 .owl-theme .owl-controls .owl-page span {
    border: 2px solid #EE82EE;
}
/*-----Accordion Style -----*/
#accordion .panel-title a:after, #accordion .panel-title a.collapsed:after {
    color:#EE82EE;
}
#accordion2 .panel-title a:after, #accordion2 .panel-title a.collapsed:after {

    color:#EE82EE;
}
#accordion2.panel-group.style-2 .panel-title a.collapsed {
    color: #ffffff;
    background:#EE82EE;
}
/*---Tab Style---*/
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color:#EE82EE;
    border-bottom: 2px solid #EE82EE;
}
.tab .nav-tabs li a:hover, .tab .nav-tabs li.active a {
    color:#EE82EE;
    border-bottom: 2px solid #EE82EE;
}
.tab.style-2 .nav-tabs li a:hover, .tab.style-2 .nav-tabs li.active a {
    color: #ffffff;
    border-bottom: 2px solid #EE82EE;
    background:#EE82EE;
}

.footer-copyright p a {
    color: #EE82EE;
}
.footer-social li a:hover i {
    background: #EE82EE;
    color: #ffffff;
}
.verticleilist.listing-shot:hover span.like-listing i, .verticleilist.listing-shot:focus span.like-listing i {
    background: #EE82EE;
    border: 1px solid #EE82EE;
}
.small-list-detail p a, p a {
    color: #EE82EE;
}
.quote-card::before {
    color:#EE82EE;
}
.quote-card cite {
    color:#EE82EE;
}
ol.check-listing > li:before, ul.check-listing > li:before {
    color: #EE82EE;
}
.service-box:before {
    border-left: 1px solid #EE82EE;
    border-right: 1px solid #EE82EE;
}
.service-box .read a:hover, .service-box:hover .service-icon i {
    color: #EE82EE;
}
.service-box:hover .service-icon i, .service-box:hover .service-content h3 a {
    color: #EE82EE;
}
.bootstrap-select.btn-group .dropdown-menu li a:hover {
    background: #EE82EE;
}
.dropdown-menu>.active>a, .dropdown-menu>.active>a:hover, .dropdown-menu>.active>a:focus {
    background-color:#EE82EE;
}
.service-box:after {
    border-bottom: 1px solid #EE82EE;
    border-top: 1px solid #EE82EE;
}
/*-----Radio button & Range Slider-------*/
.custom-radio [type="radio"]:checked + label:after,
.custom-radio [type="radio"]:not(:checked) + label:after {
    background:#EE82EE;
}
.range-slider .slider-selection {
    background:#EE82EE;
}
.range-slider .slider-handle.round {
    border: 2px solid #EE82EE;
}

@media only screen and (min-width: 1024px){
nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:hover, nav.navbar.bootsnav li.dropdown ul.dropdown-menu > li > a:focus {
    color: #EE82EE;
}
}
@media only screen and (min-width: 993px){
body nav.navbar.bootsnav.navbar-transparent ul.nav > li > a.addlist {
    background:#EE82EE !important;
}
body nav.navbar.bootsnav ul.nav > li > a.addlist {
    background:#EE82EE !important;
}
}