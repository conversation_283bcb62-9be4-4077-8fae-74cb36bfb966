<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\AuthenticationController;
use App\Http\Controllers\CompteController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\FavorisController;
use App\Http\Controllers\MemoireAltController;
use App\Http\Controllers\MemoireController;
use App\Http\Controllers\MessageAltController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\ProfessionnelController;
use App\Http\Controllers\ProfilAltController;
use App\Http\Controllers\ProfilController;
use App\Http\Controllers\ReferenceController;
use App\Http\Controllers\UsagerController;
use App\Http\Controllers\PublicController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/



Route::get('/', [AdminController::class, 'welcome'])->name('welcome');

Route::post('/login', [AuthenticationController::class, 'login'])->name('login');


// Auth middleware
Route::group([
    'middleware' => 'App\Http\Middleware\Auth',
], function () {

    Route::get('/logout', [AuthenticationController::class, 'logout'])->name('logout');

    Route::prefix('staff')->middleware('App\Http\Middleware\Admin')->group(function () {

        Route::get('dashboard', [AdminController::class, 'home'])->name('home');

        // Une route de ressource pour les références
        Route::resource('references', ReferenceController::class);
        Route::get('references/nom/add', [ReferenceController::class, 'create_name'])->name('references.nom.add');
        Route::post('references/nom/post', [ReferenceController::class, 'store_name'])->name('references.nom.post');
        Route::get('references/nom/{type}', [ReferenceController::class, 'get_name'])->name('references.nom.get');

        // Resource compte
        Route::resource('comptes', CompteController::class);

        // Resource document
        Route::resource('documents', DocumentController::class);

        // Resource memoire
        Route::resource('memoires', MemoireController::class);

        // Desactvier des documents
        Route::get('document/disable/{document_id}', [MemoireAltController::class, 'disable'])->name('memoires.disable');

        // Desactvier des documents
        Route::get('document/delete/{document_id}', [MemoireAltController::class, 'delete'])->name('memoires.delete');

        // Route ressource pour les professionnels
        Route::resource('comptes/professionnels', ProfessionnelController::class);

        // Route ressource pour les usagers
        Route::resource('comptes/usagers', UsagerController::class);

        // Importer document depuis EXCEL
        Route::post('document/import', [DocumentController::class, 'import'])->name('document.import');

        // Message pour les professionnels
        Route::get('messages', [MessageAltController::class, 'index'])->name('staff.messages.index');
        Route::get('messages/create', [MessageAltController::class, 'create'])->name('staff.messages.create');
        Route::post('messages', [MessageAltController::class, 'store'])->name('staff.messages.store');
        Route::get('messages/{message}', [MessageAltController::class, 'show'])->name('staff.messages.show');
        Route::get('messages/{message}/edit', [MessageAltController::class, 'edit'])->name('staff.messages.edit');
        Route::put('messages/{message}', [MessageAltController::class, 'update'])->name('staff.messages.update');
        Route::delete('messages/{message}', [MessageAltController::class, 'destroy'])->name('staff.messages.destroy');
        Route::post('messages/contact', [MessageAltController::class, 'contact'])->name('staff.messages.contact');
        Route::get('/messages/close/{id}', [MessageAltController::class, 'closeDiscussion'])->name('staff.messages.close');
        Route::get('/messages/close-permanently/{id}', [MessageAltController::class, 'closeDiscussionPermanently'])->name('staff.messages.permanently.close');


        // Profl Admin
        Route::get('profil', [ProfilAltController::class, 'index'])->name('staff.profil.index');
        Route::get('profil/create', [ProfilAltController::class, 'create'])->name('staff.profil.create');
        Route::get('profil/new', [ProfilAltController::class, 'new'])->name('staff.profil.new');
        Route::post('profil', [ProfilAltController::class, 'store'])->name('staff.profil.store');
        Route::get('profil/{id}', [ProfilAltController::class, 'show'])->name('staff.profil.show');
        Route::get('profil/{id}/edit', [ProfilAltController::class, 'edit'])->name('staff.profil.edit');
        Route::put('profil/{id}', [ProfilAltController::class, 'update'])->name('staff.profil.update');
        Route::delete('profil/{id}', [ProfilAltController::class, 'destroy'])->name('staff.profil.destroy');

        // Nomnre de consultation
        Route::get('consultation', [AdminController::class, 'getConsultation'])->name('staff.consultation.get');

        // Nombre d'utilisateur connecté
        Route::get('active-users', [AdminController::class, 'getActiveUser'])->name('staff.active-users.get');
    });
});

Route::get('utilisateurs', [PublicController::class, 'search'])->name('publicsearch');
Route::get('/managelist', [PublicController::class, 'manage'])->name('managelist');
Route::get('/publicmanagelist', [PublicController::class, 'manage'])->name('publicmanagelist');

// Filtre
Route::get('/rechercheAnnonce', [PublicController::class, 'rechercheAnnonce'])->name('rechercheAnnonce');
Route::get('/rechercheDocument', [PublicController::class, 'rechercheDocument'])->name('document.search');

Route::get('/detailMemoire/{id}', [PublicController::class, 'detailMemoire'])->name('detailMemoire');

Route::post('/send-email', [PublicControllerController::class, 'sendEmaill'])->name('send-email');
Route::post('/envoyer-email', [EmailController::class, 'envoyerEmail'])->name('envoyer-email');
Route::get('/sendEmail', [PublicController::class, 'sendEmail'])->name('email.send');
Route::post('/effectuer-action', [PublicController::class, 'effectuerAction'])->name('effectuer-action');
Route::post('/envoi-mail', [PublicController::class, 'envoiMail'])->name('envoi-mail');
Route::post('/export-pdf', [PublicController::class, 'exportPDF'])->name('export-pdf');


Route::get('/download-pdf',  [PublicController::class, 'dowloapdf'])->name('datatablepdf');
//Route::get('datatable-pdf', 'DataTableController@downloadPDF')->name('datatable.pdf');
Route::get('/download/{filename}', [PublicController::class, 'dowloadpdf'])->name('download');

// Message ressource
Route::resource('/messages', MessageController::class);
Route::get('/messages/close/{id}', [MessageController::class, 'closeDiscussion'])->name('message.close');

// Profil ressource
Route::resource('/profil', ProfilController::class);

// Favoris ressource
Route::resource('/favoris', FavorisController::class);

// Client : creation de compte
Route::post('/creation-compte', [UsagerController::class, 'store'])->name('client.store');
