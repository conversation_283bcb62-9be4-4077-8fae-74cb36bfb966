



<?php $__env->startSection('css'); ?>
    <!-- Common Style -->
    <link href="<?php echo e(asset('assets/css/style.css')); ?>" rel="stylesheet" />


    <style>
        .custom-checkbox-1 input[type="checkbox"]:checked+label:after {
            left: 11px;
        }

        .custom-checkbox-2 input[type="checkbox"]:checked+label:after {
            top: 5px;
        }

        .facette-color {
            background-color: #DFF3FE;
        }

        /* Spinner */
        .lds-dual-ring {
            display: inline-block;
            width: 40px;
            height: 40px;
        }

        .lds-dual-ring:after {
            content: " ";
            display: block;
            width: 24px;
            height: 24px;
            margin: 8px;
            border-radius: 50%;
            border: 4px solid #EA4F0C;
            border-color: #EA4F0C transparent #EA4F0C transparent;
            animation: lds-dual-ring 1.2s linear infinite;
        }

        @keyframes  lds-dual-ring {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="wrapper">
        <?php echo $__env->make('layout_client.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <div class="clearfix"></div>

        <section class="title-transparent page-title" style="background-image:url(/assets_client/img/bibioteque_1200x680_bibl.jpg);" data-overlay="8" style="padding-bottom: 0px;">
            <div class="container">
                <div class="banner-caption">
                    <div class="col-md-12 col-sm-12 banner-text">
                        <?php echo $__env->make('layout_client.search-form', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                </div>
            </div>
        </section>
    </div>


    <div class="clearfix"></div>

    <input type="hidden" id="metaData" data-url="<?php echo e(route('document.search')); ?>" data-type="<?php echo e($type); ?>" data-cle="<?php echo e($cle); ?>">
    <section class="show-case">
        <div class="container">
            <div class="row">
                <div class="col-md-4 col-sm-12">
                    <h4 class="text-center mrg-bot-15">Filtrer vos recherches</h4>
<a href="/sendEmail" class="button">Email</a>
                    <?php if($filtres_list): ?>
                        <a href="javascript:void(0)" id="filtre-reset">
                            <h4 class="text-center mrg-bot-10">
                                <span class="badge height-25" style="background-color: #EA4F0C">
                                    x Effacer tous les filtres
                                </span>
                            </h4>
                        </a>
                    <?php endif; ?>

                    <div class="sidebar" id="facette-zone">
                        <form style="padding: 0px; margin: 0px;" id="filterForm" method="GET" action="<?php echo e(route('rechercheAnnonce')); ?>">
                            <?php $__currentLoopData = $facettes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $facette): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php if($facette['data']): ?>
                                    <div class="widget-boxed facette-color" id="facette-<?php echo e($facette['id']); ?>" style="padding-bottom: 0px; margin-bottom: 10px;">
                                        <div class="widget-boxed-header">
                                            <h4><i class="<?php echo e($facette['icon']); ?> padd-r-10"></i><?php echo e($facette['nom']); ?></h4>
                                        </div>
                                        <div class="widget-boxed-body padd-top-10 padd-bot-0" id="facette-sub-<?php echo e($facette['id']); ?>">
                                            <div class="side-list">
                                                <ul class="price-range" id="list-<?php echo e($facette['id']); ?>">
                                                    <?php $__currentLoopData = $facette['data']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <li style="padding: 0px; display: none;">
                                                            <span class="custom-checkbox custom-checkbox-2 d-block">
                                                                <input type="checkbox" <?php echo e($data['isChecked'] ? 'checked' : ''); ?> id="<?php echo e($data['slug']); ?>" data-slug=<?php echo e($data['slug']); ?> class="filter-checkbox" data-index="<?php echo e($facette['index']); ?>" data-type="<?php echo e($facette['type']); ?>" data-colonne="<?php echo e($facette['colonne']); ?>" data-id="<?php echo e($data['id']); ?>" data-valeur="<?php echo e($data['valeur']); ?>">
                                                                <label></label>
                                                                <?php echo e($data['valeur']); ?> (<?php echo e($data['nbre']); ?>)
                                                            </span>
                                                        </li>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </ul>
                                            </div>
                                        </div>

                                        <?php if(count($facette['data']) > 5): ?>
                                            <div class="widget-boxed-header text-center padd-top-5 padd-bot-5" id="voir-plus-zone-<?php echo e($facette['id']); ?>">
                                                <a href="javascript:void(0)" id="voir-plus-btn-<?php echo e($facette['id']); ?>">
                                                    <h5>Voir plus (<?php echo e(count($facette['data']) - 5); ?>) +</h5>
                                                </a>
                                            </div>
                                        <?php endif; ?>

                                    </div>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <input type="hidden" name="type_document" id="type_document" value="<?php echo e($type); ?>">
                            <input type="hidden" name="mot_cle" id="mot_cle" value="<?php echo e($cle); ?>">
                            <input type="hidden" name="filtres" id="filtres" value="<?php echo e($filtres); ?>">
                            <input type="hidden" name="filtre" id="filtre">
                        </form>
                    </div>
                </div>
                <div class="col-md-8 col-sm-12">
                    <div class="card">
                        <?php if($filtres_list): ?>
                            <div class="card-header">
                                <div class="col-md-12" style="margin-left: 0px; padding-left: 0px;">
                                    Recherche :
                                    <?php $__currentLoopData = $filtres_list; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="badge height-25" style="background-color: #EA4F0C">
                                            <?php echo e($element['valeur']); ?>

                                            <a href="javascript:void(0)" class="filtre" data-slug="<?php echo e($element['slug']); ?>" style="color: #35434E"> x </a>
                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="card-header" style="padding: 0px;">
                            <div class="col-md-4" style=" margin-top: 10px !important;">
                                <span style="font-size: 15px;" id="nbre-resultat">
                                    <?php echo e($nbreResultat); ?> résultats
                                </span>
                            </div>
                            <div class="col-md-8 text-right" id="pagination-resultat">
                                <?php echo e($paginator->appends(['filtre' => '', 'filtres' => $filtres])->links()); ?>

                            </div>
                        </div>

                        <div class="card-header">
                            <div class="col-md-3" style="margin-left: 0px; padding-left: 0px;">
                                <select class="form-control" id="select-order" style="height: 35px !important; margin-bottom: 0px;" tabindex="-98">
                                    <option value="">Trier</option>
                                    <option value="asc" data-donnee="data-titre" data-type="text">Titre: A à Z</option>
                                    <option value="desc" data-donnee="data-titre" data-type="text">Titre: Z à A</option>
                                    <option value="asc" data-donnee="data-auteur" data-type="text">Auteur: A à Z</option>
                                    <option value="desc" data-donnee="data-auteur" data-type="text">Auteur: Z à A</option>
                                    <option value="asc" data-donnee="data-date" data-type="date">Date de soutenance: A à Z</option>
                                    <option value="desc" data-donnee="data-date" data-type="date">Date de soutenance: Z à A</option>
                                </select>
                            </div>
                            <div class="col-md-5"></div>
                            <div class="col-md-3" style="margin-right: 0px;">
                                <form id="action-form" action="<?php echo e(route('effectuer-action')); ?>" method="POST">
                                    <?php echo csrf_field(); ?>
                                <select class="form-control" name="action" id="send-email" style="height: 35px !important; margin-bottom: 0px;" tabindex="-98">
                                    <option value=" ">Action</option>
                                    <option value="export-pdf">Exporter la liste des résultats en pdf</option>
                                    <option value="25">Mettre dans les favoris</option>
                                    <option value="imprimer">Imprimer</option>
                                    <option value="envoi-mail" >Envoyer par mail</option>
                                </select></form>

                            </div>
                            <div class="col-md-1 text-right" style="vertical-align: middle; margin-right: 0px;">
                                <span class="custom-checkbox" style="">
                                    <input type="checkbox" class="checkbox_table" name="options[]" value="1">
                                    <label for="checkbox1"></label>
                                </span>
                            </div>
                        </div>

                        <div class="card-body" style="padding-right: 0px;">
                            <div class="table-responsive">

                                <h5 id="tr-loader" class="text-center" style="display: none;">
                                    <div class="lds-dual-ring"></div>
                                </h5>
                                <h5 id="tr-empty" style="display: none;" class="text-center">Aucun résultat trouvé</h5>

                                <table id="datatable">

                                    <tbody id="filtered-results">
                                        <?php $__currentLoopData = $documents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $document): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr id="row-<?php echo e($document->id); ?>">
                                                <td style="background-color: white; padding: 0px;" data-titre="<?php echo e($document->titre); ?>" data-auteur="<?php echo e($document->auteur); ?>" data-date="<?php echo e($document->memoire->date_soutenance); ?>">
                                                    <div class="verticleilist listing-shot facette-color" style="margin: 0px; margin-bottom: 15px; border-color: #BDD8DC;">
                                                        <a class="listing-item" href="<?php echo e(route('detailMemoire', ['id' => $document->id])); ?>">
                                                            <div class="listing-shot-img">
                                                                <?php if($document->image_id): ?>
                                                                    <img src="<?php echo e(asset($document->image->chemin)); ?>" width="200" height="200" class="img-responsive" height="90%" alt="">
                                                                <?php else: ?>
                                                                    <img src="http://via.placeholder.com/800x850" class="img-responsive" height="90%" alt="">
                                                                <?php endif; ?>
                                                            </div>
                                                        </a>
                                                        <div class="verticle-listing-caption">
                                                            <div class="listing-shot-caption">
                                                                <a href="<?php echo e(route('detailMemoire', ['id' => $document->id])); ?>">
                                                                    <h4><?php echo e($document->titre); ?></h4>
                                                                </a>
                                                                <span>
                                                                    <strong>Par </strong>: <?php echo e($document->auteur); ?>

                                                                </span>
                                                            </div>
                                                            <div class="listing-shot-info">
                                                                <div class="row extra">
                                                                    <div class="col-md-12">
                                                                        <div class="listing-detail-info">
                                                                            <span style="font-weight: bold;">
                                                                                <?php if($document->memoire->niveau_etude): ?>
                                                                                    <?php echo e($document->memoire->ref_niveau_etude->valeur); ?>

                                                                                <?php endif; ?>
                                                                            </span>
                                                                            <span>
                                                                                <p class="listing-description">
                                                                                    <?php echo e(substr($document->resume, 0, 100)); ?>...
                                                                                </p>
                                                                            </span>
                                                                            <span>
                                                                                <?php if($document->type == 'Mémoire'): ?>
                                                                                    <strong>Date de soutenance</strong>: <?php echo e(date('d/m/Y', strtotime($document->memoire->date_soutenance))); ?>

                                                                                <?php endif; ?>
                                                                            </span>
                                                                            <style>
                                                                                a.sujet:hover {
                                                                                    color: #EA4F0C;
                                                                                }
                                                                            </style>
                                                                            <span>
                                                                                <strong>Sujet</strong>:
                                                                                <?php $__currentLoopData = $document->ref_sujet; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sujet): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                                    <a href="javascript:void(0)" class="sujet" data-id="<?php echo e($sujet->sujet->id); ?>">
                                                                                        <?php echo e($sujet->sujet->valeur); ?>

                                                                                    </a> ,
                                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                            </span>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>

                                                            <div class="listing-shot-info rating">
                                                                <div class="row extra">
                                                                    <div class="col-md-3 col-sm-3 col-xs-6" style="margin: 0px !important;">
                                                                        <?php if(Auth::check()): ?>
                                                                            <?php if($document->favori): ?>
                                                                                <a href="javascript:void(0)" id="f-0" class="favoris orange-color favoris-<?php echo e($document->id); ?>" data-id="<?php echo e($document->id); ?>" data-url="<?php echo e(route('favoris.store')); ?>" data-token="<?php echo e(csrf_token()); ?>">
                                                                                    <i class="color fa fa-heart" class="orange-color favoris-<?php echo e($document->id); ?>" aria-hidden="true"></i>
                                                                                    Favoris
                                                                                </a>
                                                                            <?php else: ?>
                                                                                <a href="javascript:void(0)" id="f-0" class="favoris favoris-<?php echo e($document->id); ?>" data-id="<?php echo e($document->id); ?>" data-url="<?php echo e(route('favoris.store')); ?>" data-token="<?php echo e(csrf_token()); ?>">
                                                                                    <i class="color fa fa-heart" class="favoris-<?php echo e($document->id); ?>" aria-hidden="true"></i>
                                                                                    Favoris
                                                                                </a>
                                                                            <?php endif; ?>
                                                                        <?php else: ?>
                                                                            <a href="javascript:void(0)" data-toggle="modal" data-target="#signin">
                                                                                <i class="favoris color fa fa-heart"aria-hidden="true"></i>
                                                                                Favoris
                                                                            </a>
                                                                        <?php endif; ?>
                                                                    </div>
                                                                    <div class="col-md-3 col-sm-3 col-xs-6" style="margin: 0px !important;">
                                                                        <a href="javascript:void(0)" class="">
                                                                            <i class="color fa fa-share" aria-hidden="true"></i>
                                                                            Exporter
                                                                        </a>
                                                                    </div>
                                                                    <div class="col-md-3 col-sm-3 col-xs-6" style="margin: 0px !important;">
                                                                        <a href="javascript:void(0)" class="">
                                                                            <i class="color fa fa-share-alt" aria-hidden="true"></i>
                                                                            Partager
                                                                        </a>
                                                                    </div>
                                                                    <div class="col-md-3 col-sm-3 col-xs-6" style="margin: 0px !important; padding: 0px !important;">
                                                                        <a href="javascript:void(0)" class="">
                                                                            <i class="color fa fa-star" style="" aria-hidden="true"></i>
                                                                            Demander
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td style="vertical-align: middle; padding: 0px;">
                                                    <span class="custom-checkbox custom-checkbox-1" style="width: 5px; margin: 0px;">
                                                        <input type="checkbox" class="checkbox_table text-right" name="options[]" value="1" style=" margin-left: 5px;">
                                                        <label class="text-center" for="checkbox1" style="margin: 0px; margin-left: 5px;"></label>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                        <?php if($documents->isEmpty()): ?>
                                            <h5 class="text-center">Aucun résultat trouvé</h5>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>


                    </div>
                </div>
                
            </div>
        </div>
    </section>
    <!-- ================ End Listing In Vertical style with Sidebar ======================= -->

    <!-- ================ Start Footer ======================= -->
    <?php echo $__env->make('layout_client.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <!-- ================ End Footer Section ======================= -->

    <!-- ================== Login & Sign Up Window ================== -->
    <?php echo $__env->make('layout.connexion_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <!-- ===================== End Login & Sign Up Window =========================== -->

    <?php echo $__env->make('layout_client.scroller', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
        <script>
            $(document).ready(function() {
                $('#signin').modal('show');
            });
        </script>
    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    <script src="<?php echo e(asset('custom/js/search-list.js')); ?>"></script>
    <script>
        $(document).ready(function() {
            $('#datatable').DataTable();
        });
    </script>

    

    <script>
        document.getElementById('send-email').addEventListener('change', function() {
            var selectedAction = this.value;

            if (selectedAction === 'imprimer') {
                window.print();
            } else if (selectedAction === 'envoi-mail') {
                // Code pour envoyer par courrier électronique
                var documents = []; // Récupérez les données du datatable
                var dataTable = document.getElementById('datatable');
var dataTableData = [];

// Parcours des lignes du tableau
for (var i = 0; i < dataTable.rows.length; i++) {
    var row = dataTable.rows[i];
    var rowData = [];

    // Parcours des cellules de chaque ligne
    for (var j = 0; j < row.cells.length; j++) {
        var cell = row.cells[j];
        rowData.push(cell.textContent);
    }

    dataTableData.push(rowData);
}

//console.log(dataTableData);

                //var dataTable = $('#datatable').DataTable();
                //alert('dataTable');
           // var documents = dataTable.rows().data().toArray();
 alert(dataTableData);

            var form = document.getElementById('action-form');
            var input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'data';
            input.value = JSON.stringify(dataTableData);
            form.appendChild(input);
            form.submit();
            } else if (selectedAction === 'export-pdf') {
                var documents = []; // Récupérez les données du datatable
                var dataTable = $('#datatable').DataTable();
                var documents = dataTable.rows().data().toArray();
                var form = document.getElementById('action-form');
                var input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'data';
                input.value = JSON.stringify(documents);
                form.appendChild(input);
                form.submit();
            }
        });
    </script>
        


    <script>
        const select = document.getElementById('choix');
        select.addEventListener('change', (event) => {
            const selectedOption = event.target.value;
            // Effectuer l'action souhaitée avec la valeur sélectionnée
            // Par exemple, envoyer un e-mail en utilisant AJAX
            // Vous pouvez appeler une fonction JavaScript ou effectuer une requête AJAX ici
        });
    </script>
    
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout_client.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-document\resources\views/client/search-list.blade.php ENDPATH**/ ?>