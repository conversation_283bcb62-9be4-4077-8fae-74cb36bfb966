<nav class="navbar navbar-default navbar-fixed navbar-transparent white bootsnav">
    <div class="container-fluid">
        <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar-menu">
            <i class="ti-align-left"></i>
        </button>

        <!-- Start Header Navigation -->
        <div class="navbar-header">
            <a class="navbar-brand" href="/">
                <img src="<?php echo e(asset('custom/img/logo9.png')); ?>" class="logo logo-display" alt="">
                <img src="<?php echo e(asset('custom/img/logo9.png')); ?>" class="logo logo-scrolled" alt="">
            </a>
        </div>

        <!-- Collect the nav links, forms, and other content for toggling -->
        <div class="collapse navbar-collapse" id="navbar-menu">
            <ul class="nav navbar-nav navbar-center" data-in="fadeInDown" data-out="fadeOutUp">
                <li>
                    <a href="<?php echo e(route('welcome')); ?>">Accueil</a>
                </li>
                <?php if(auth()->check()): ?>
                    <li>
                        <a href="<?php echo e(route('messages.index')); ?>">Contact</a>
                    </li>
                <?php else: ?>
                    <li>
                        <a href="<?php echo e(route('messages.create')); ?>">Contact</a>
                    </li>
                <?php endif; ?>
                <?php if(auth()->check() && auth()->user()->is_admin): ?>
                    <li>
                        <a href="<?php echo e(route('home')); ?>" target="_blank">Accès professionnel</a>
                    </li>
                <?php endif; ?>

            </ul>

            <?php if(!auth()->check()): ?>
                <ul class="nav navbar-nav navbar-right" data-in="fadeInDown" data-out="fadeOutUp">
                    <li class="no-pd">
                        <a href="javascript:void(0)" data-toggle="modal" data-target="#signin" class="addlist">
                            <i class="ti-user" aria-hidden="true"></i>Connexion
                        </a>
                    </li>
                </ul>
            <?php else: ?>
                <ul class="nav navbar-nav navbar-right" data-in="fadeInDown" data-out="fadeOutUp">
                    <li class="no-pd dropdown">
                        <a href="javascript:void(0)" class="addlist">
                            <img src="<?php echo e(asset('assets_client/img/avatar.jpg')); ?>" class="img-responsive img-circle avater-img" alt="">
                            <strong><?php echo e(auth()->user()->nom); ?> <?php echo e(auth()->user()->prenom); ?></strong>
                        </a>
                        <ul class="dropdown-menu animated navbar-left fadeOutUp" style="display: none; opacity: 1;">
                            <li>
                                <a href="<?php echo e(route('profil.index')); ?>">
                                    <i class="fa fa-user" aria-hidden="true"></i> &nbsp;
                                    Mon compte
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo e(route('messages.index')); ?>">
                                    <i class="fa fa-envelope" aria-hidden="true"></i> &nbsp;
                                    Contact
                                </a>
                            <li>
                                <a href="<?php echo e(route('favoris.index')); ?>">
                                    <i class="fa fa-heart" aria-hidden="true"></i> &nbsp;
                                    Favoris
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo e(route('logout')); ?>">
                                    <i class="fa fa-power-off" aria-hidden="true"></i> &nbsp;
                                    Déconnexion
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            <?php endif; ?>
        </div>
        <!-- /.navbar-collapse -->
    </div>
</nav>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-document\resources\views/layout_client/navbar.blade.php ENDPATH**/ ?>