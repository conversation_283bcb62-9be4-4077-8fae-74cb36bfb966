<?php if($paginator->hasPages()): ?>
    <nav class="navbar-right" style="transform: scale(0.8); margin: 0px; padding: 0px;">
        <ul class="pagination" style="margin: 0px; padding: 0px;">
            
            <?php if($paginator->onFirstPage()): ?>
                <li class="disabled" aria-disabled="true" aria-label="<?php echo app('translator')->get('pagination.previous'); ?>">
                    <a href="javascript:void(0)" aria-hidden="true">«</a>
                </li>
            <?php else: ?>
                <li>
                    <a href="<?php echo e($paginator->previousPageUrl()); ?>" rel="prev" aria-label="<?php echo app('translator')->get('pagination.previous'); ?>">«</a>
                </li>
            <?php endif; ?>

            
            <?php $__currentLoopData = $elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                
                <?php if(is_string($element)): ?>
                    <li class="disabled" aria-disabled="true">
                        <a href="javascript:void(0)"><?php echo e($element); ?></a>
                    </li>
                <?php endif; ?>

                
                <?php if(is_array($element)): ?>
                    <?php $__currentLoopData = $element; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($page == 1 && $page != $paginator->currentPage()): ?>
                            <li>
                                <a href="<?php echo e($url); ?>"><?php echo e($page); ?></a>
                            </li>
                        <?php endif; ?>

                        <?php if($page == $paginator->currentPage()): ?>
                            <li class="active" aria-current="page">
                                <a href="javascript:void(0)"><?php echo e($page); ?></a>
                            </li>
                        
                        <?php endif; ?>
                        
                        <?php if($page == $paginator->lastPage() && $page != $paginator->currentPage()): ?>
                            <li>
                                <a href="<?php echo e($url); ?>"><?php echo e($page); ?></a>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            
            <?php if($paginator->hasMorePages()): ?>
                <li>
                    <a href="<?php echo e($paginator->nextPageUrl()); ?>" rel="next" aria-label="<?php echo app('translator')->get('pagination.next'); ?>">»</a>
                </li>
            <?php else: ?>
                <li class="disabled" aria-disabled="true" aria-label="<?php echo app('translator')->get('pagination.next'); ?>">
                    <a href="javascript:void(0)" aria-hidden="true">»</a>
                </li>
            <?php endif; ?>
        </ul>
    </nav>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-document\resources\views/vendor/pagination/bootstrap-4.blade.php ENDPATH**/ ?>