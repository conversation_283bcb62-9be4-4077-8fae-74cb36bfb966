

<?php $__env->startSection('content'); ?>
    <div class="wrapper">
        <!-- Start Navigation -->
        <?php echo $__env->make('layout_client.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- End Navigation -->
        <div class="clearfix"></div>

        

        <!-- Main Banner Section Start -->
        <div class="banner dark-opacity" style="background-image:url(/assets_client/img/bibioteque_1200x680_bibl.jpg);" data-overlay="8">
            <div class="container">
                <div class="banner-caption">
                    <div class="col-md-12 col-sm-12 banner-text">
                        <form method="GET" action="<?php echo e(route('rechercheAnnonce')); ?>" class="form-verticle">
                            <div class="col-md-4 col-sm-4 no-padd">
                                <div class="form-box">
                                    <i class="banner-icon icon-layers"></i>
                                    <select class="selectpicker form-control " id="type_document" name="type_document" data-live-search="true" tabindex="-98">
                                        <option value="all" selected class="chosen-select">Tous les Documents</option>
                                        <option value="Memoire">Mémoire </option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-7 col-sm-7 no-padd">
                                <div class="form-box btn-search" >
                                    <i class="banner-icon icon-pencil"></i>
                                    <input type="text" class="form-control right-br" id="mot_cle" name="mot_cle" placeholder="Mot clé ...">
                                </div>
                            </div>

                            <div class="col-md-1 col-sm-1 no-padd">
                                <div class="form-box">
                                    <button type="submit" class="btn btn-default" style="background-color: #EA4F0C; border-top-right-radius: 20px; border-bottom-right-radius: 20px;">
                                        <i class="fa fa-fw fa-search" style="color: white; font-size: 25px;"></i>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="clearfix"></div>
        <!-- Main Banner Section End -->


        <!-- ================ Start Footer ======================= -->
        <?php echo $__env->make('layout_client.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- ================ End Footer Section ======================= -->

        <!-- ================== Login & Sign Up Window ================== -->
        <?php echo $__env->make('layout.connexion_modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <!-- ===================== End Login & Sign Up Window =========================== -->

        <?php echo $__env->make('layout_client.scroller', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <!-- START JAVASCRIPT -->



    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
        <script>
            $(document).ready(function() {
                $('#signin').modal('show');
            });
        </script>
    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>

    <script>
        $(document).ready(function() {
            $('#btn-register').click(function() {
                $('#signin').modal('hide');

                setTimeout(function() {
                    $('#register').modal('show');
                }, 500);
            });

            $('#btn-login').click(function() {
                $('#register').modal('hide');

                // Attendre une seconde avant d'afficher le modal
                setTimeout(function() {
                    $('#signin').modal('show');
                }, 500);
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layout_client.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Documents\GitHub\numdoc-document\resources\views/welcome.blade.php ENDPATH**/ ?>